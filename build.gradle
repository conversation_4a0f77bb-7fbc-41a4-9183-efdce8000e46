plugins {
    id 'org.springframework.boot' version '2.2.4.RELEASE'
    id 'io.spring.dependency-management' version '1.0.10.RELEASE'
    id 'java'
    id "groovy"
    id 'jacoco'
    id "org.sonarqube" version "3.0"
}

group = 'com.concirrus.submissions'
version = (findProperty('version') == 'unspecified') ? '1' : version
def commitHash = findProperty('commit_hash').with { (it == 'unspecified') ? '' : it }
sourceCompatibility = 1.11

ext {
    set('springCloudGcpVersion', "3.7.7")
}

repositories {
    maven {
        url "https://nexus.submission.concirrusquest.com/repository/maven-releases/"
        credentials {
            username project.findProperty('nexusUsername')?.toString() ?: "submission-nexus"
            password project.findProperty('nexusPassword')?.toString() ?: "submission@nexus"
        }
    }
    mavenCentral()
}

dependencies {
    implementation 'junit:junit:4.12'
    implementation 'org.apache.logging.log4j:log4j-api:2.16.0'
    implementation 'org.apache.httpcomponents:httpclient:4.5.5'
    implementation 'com.concirrus.quest:quest-cloud-kubernetes-config:1.0.0'
    implementation 'com.concirrus.submission:commons-lib:1.0.4'
// https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-databind
    compile group: 'com.fasterxml.jackson.core', name: 'jackson-databind', version: '2.11.2'
    implementation ('com.concirrus.submission:submission-manager-connector:199906')
    implementation ('com.concirrus.submission:submission-management-connector:163111')
    implementation ('com.concirrus.submission:access-management-connector:171358')
    implementation('org.springframework.boot:spring-boot-starter-web')
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    implementation "com.concirrus.submission:workflow-manager-connector:163169"
    implementation "com.concirrus:submission-handler-connector:204763"
    implementation "com.concirrus.submissions:quest-insights-connector:160206"
    implementation 'com.concirrus.submission:policy-integration-connector:3053'
    implementation "com.concirrus.submission:notes-service-connector:197742"
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'org.springframework.boot:spring-boot-starter'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'com.graphql-java-kickstart:graphql-spring-boot-starter:7.1.0'
    implementation 'com.graphql-java-kickstart:graphiql-spring-boot-starter:7.1.0'
    implementation "com.graphql-java-kickstart:graphql-java-tools:6.2.0"
    implementation group: 'com.graphql-java', name: 'graphql-java-servlet', version: '6.1.0'
    implementation 'org.projectlombok:lombok:1.18.28'
    annotationProcessor 'org.projectlombok:lombok:1.18.28'
//    implementation 'org.springframework.security.oauth:spring-security-oauth2:2.3.4.RELEASE'
//    implementation 'org.springframework.security:spring-security-jwt:1.0.9.RELEASE'

    implementation('io.springfox:springfox-swagger2:2.9.2')
    implementation('io.springfox:springfox-swagger-ui:2.9.2')

    testImplementation('org.springframework.boot:spring-boot-starter-test') {
        exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
    }
    implementation 'org.owasp.encoder:encoder:1.2.3'

    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'io.github.resilience4j:resilience4j-spring-boot2:1.7.1'
    implementation 'io.github.resilience4j:resilience4j-ratelimiter:1.7.1'

    implementation 'org.springframework.cloud:spring-cloud-starter-aws-messaging:2.2.6.RELEASE'
    implementation "org.springframework.integration:spring-integration-core"
    implementation('com.google.cloud:spring-cloud-gcp-starter-pubsub') {
        exclude group: 'com.google.guava', module: 'guava'
    }
    implementation 'com.google.guava:guava:32.0.0-android'
    implementation 'org.springframework.boot:spring-boot-starter-websocket'
}

dependencyManagement {
    imports {
        mavenBom "com.google.cloud:spring-cloud-gcp-dependencies:${springCloudGcpVersion}"
    }
}


processResources {
    filesMatching("actuator.properties") {
        expand(["version": project.version, "commit_hash": commitHash])
    }
}


// Task for pre-downloading all dependencies
task downloadDependencies {
    description "Pre-downloads *most* dependencies"
    doLast {
        configurations.getAsMap().each { name, config ->
            println "Retrieving dependencies for $name"
            try {
                config.files
            } catch (e) {
                project.logger.info e.message // some cannot be resolved, silentlyish skip them
            }
        }
    }
}

tasks['sonarqube'].dependsOn test // need to check if required or can be removed

sonarqube {
    properties {
        property 'sonar.coverage.exclusions', "**/com/concirrus/submissions/common/**," +
                "**/com/concirrus/submissions/config/**," +
                "**/com/concirrus/submissions/model/**," +
                "**/com/concirrus/submissions/integration/**," +
                "**/com/concirrus/submissions/graphql/model/**," +
                "**/com/concirrus/submissions/SubmissionGatewayApplication.java"
    }
}

test {
    if (!project.hasProperty('IntegrationTests')) {
        exclude '**/*IntegrationTest.class'
    }
}

jacocoTestReport {
    group = "Reporting"
    reports {
        xml.enabled true
    }
}


