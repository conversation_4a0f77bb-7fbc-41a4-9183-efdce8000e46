FROM openjdk:8-jdk-alpine as builder

ARG VERSION
RUN echo $VERSION

ARG COMMIT_HASH
RUN echo $COMMIT_HASH

ARG SONAR_URL
RUN echo $SONAR_URL

ARG SONAR_LOGIN
RUN echo $SONAR_LOGIN

ARG CI_COMMIT_SHA
RUN echo $CI_COMMIT_SHA

ARG CI_COMMIT_REF_NAME
RUN echo $CI_COMMIT_REF_NAME

ARG CI_PROJECT_ID
RUN echo $CI_PROJECT_ID

ARG GITLAB_ACCESS_TOKEN
RUN echo $GITLAB_ACCESS_TOKEN

ENV SRC_ROOT=/home/
WORKDIR $SRC_ROOT

COPY . $SRC_ROOT
RUN ./gradlew --version

RUN ./gradlew test
RUN ./gradlew sonarqube -Dsonar.host.url=$SONAR_URL -Dsonar.login=$SONAR_LOGIN  -Dsonar.gitlab.commit_sha=$CI_COMMIT_SHA -Dsonar.gitlab.ref_name=$CI_COMMIT_REF_NAME -Dsonar.gitlab.project_id=$CI_PROJECT_ID -Dsonar.gitlab.user_token=$GITLAB_ACCESS_TOKEN -Dsonar.scm.provider=git --stacktrace jacocoTestReport