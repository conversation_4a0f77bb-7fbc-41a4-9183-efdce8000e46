replicaCount: 1

image:
  # override docker image tag, which defaults to appVersion defined in Chart.yaml
  # tag: 10-beta.1 

  repository: 639966507663.dkr.ecr.eu-west-2.amazonaws.com/submission-gateway-service
  pullPolicy: IfNotPresent

nameOverride: ""
fullnameOverride: "submission-gateway-service"

service:
  type: ClusterIP
  port: 80

app:
  envs:
  containerPort: 9005

  secretWithEnvs:
    create: false # please consider that creating secret is possible if secreted with name provided does not already exists
    name: "" # when name is not provided, deployment name is used
    envs: {}
      # ENV_NAME:
      #   key: key
    #   value: value

ingress:
  enabled: false
  annotations:
    kubernetes.io/ingress.class: traefik
  paths: []
  hosts:
    - chart-example.local
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

livenessProbe:
  enabled: true
  path: /submission-gateway-service/manage/health
  initialDelaySeconds: 100 #60
  periodSeconds: 10
  timeoutSeconds: 20 #5
  successThreshold: 1
  failureThreshold: 15 #3

readinessProbe:
  enabled: true
  path: /submission-gateway-service/manage/health
  initialDelaySeconds: 100 #30
  periodSeconds: 10
  timeoutSeconds: 20 #3
  successThreshold: 1
  failureThreshold: 15 #3

resources:
  requests:
    cpu: 100m #50m
    memory: 350Mi #256Mi
  limits:
    cpu: 500m
    memory: 768Mi

nodeSelector: {}

tolerations: []

affinity: {}

