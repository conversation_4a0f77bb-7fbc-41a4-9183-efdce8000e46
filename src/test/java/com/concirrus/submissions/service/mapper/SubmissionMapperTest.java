package com.concirrus.submissions.service.mapper;

import com.concirrus.submissions.graphql.model.SubmissionEditRequest;
import com.concirrus.submissions.graphql.model.VesselInfoInput;
import com.concirrus.submissions.model.SubmissionCreationRequest;
import org.jetbrains.annotations.NotNull;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
public class SubmissionMapperTest {
    private static final String TEST_STRING = "test";

    @Mock
    SubmissionMapper submissionMapper;

    @Before
    public void setUp() {
        submissionMapper = new SubmissionMapper();
    }

    @Test
    public void testGetInstance() {
        submissionMapper.getInstance(new SubmissionEditRequest(),"TEST");
    }

    @Test
    public void testGetInstanceSubmissionCreateRequest() {
        submissionMapper.getInstance(TEST_STRING, new SubmissionCreationRequest());
    }

    @Test
    public void testGetInstanceVesselsNotEmpty() {
        SubmissionEditRequest submissionEditRequest = new SubmissionEditRequest();
        submissionEditRequest.setVessels(getVesselsInput());
        submissionMapper.getInstance(submissionEditRequest,"test");
    }

    @Test
    public void testGetInstanceSubmissionCreateRequestVesselsNotEmpty() {
        SubmissionCreationRequest submissionCreationRequest = new SubmissionCreationRequest();
        submissionCreationRequest.setVessels(getVesselsInput());
        submissionMapper.getInstance(TEST_STRING, submissionCreationRequest);
    }


    @NotNull
    private List<String> getImos() {
        List<String> imos = new ArrayList<>();
        imos.add("9993928");
        imos.add("9932828");
        return imos;
    }

    @NotNull
    private List<VesselInfoInput> getVesselsInput() {
        List<VesselInfoInput> vessels = new ArrayList<>();

        VesselInfoInput vessel1 = new VesselInfoInput();
        vessel1.setImo("9993928");
        vessel1.setProductType(TEST_STRING);
        vessel1.setCoverFromDate(ZonedDateTime.now().toString());
        vessel1.setCoverToDate(ZonedDateTime.now().toString());
        vessel1.setPremium(0.0);
        vessel1.setDeductible(0.0);
        vessel1.setSumInsured(0.0);
        vessel1.setIncreasedValue(0.0);
        vessel1.setPremiumCurrencyCode(TEST_STRING);
        vessel1.setSumInsuredCurrencyCode(TEST_STRING);
        vessels.add(vessel1);

        VesselInfoInput vessel2 = new VesselInfoInput();
        vessel2.setImo("9932828");
        vessel2.setProductType(TEST_STRING);
        vessel2.setCoverFromDate(ZonedDateTime.now().toString());
        vessel2.setCoverToDate(ZonedDateTime.now().toString());
        vessel2.setPremium(0.0);
        vessel2.setDeductible(0.0);
        vessel2.setSumInsured(0.0);
        vessel2.setIncreasedValue(0.0);
        vessel2.setPremiumCurrencyCode(TEST_STRING);
        vessel2.setSumInsuredCurrencyCode(TEST_STRING);
        vessels.add(vessel2);

        return vessels;
    }


}
