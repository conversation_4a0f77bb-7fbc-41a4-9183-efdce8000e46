package com.concirrus.submissions.listener;

import com.concirrus.submission.connector.submissionmanager.model.*;
import com.concirrus.submissions.integration.AviationSubmissionChangesService;
import com.concirrus.submissions.integration.AviationWarSubmissionChangeService;
import com.concirrus.submissions.integration.ConstructionSubmissionChangesService;
import com.concirrus.submissions.integration.SubmissionChangesService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@RunWith(SpringJUnit4ClassRunner.class)
public class QueueFacadeTest {

    @Mock
    ObjectMapper objectMapper;

    @Mock
    SubmissionChangesService submissionChangesService;

    @Mock
    AviationSubmissionChangesService aviationSubmissionChangesService;

    @Mock
    ConstructionSubmissionChangesService constructionSubmissionChangesService;

    @Mock
    AviationWarSubmissionChangeService aviationWarSubmissionChangeService;


    @Mock
    QueueFacade queueFacade;

    private static final String TEST_STRING = "test";

    @Before
    public void setUp() {
        queueFacade = new QueueFacade(objectMapper,submissionChangesService, aviationSubmissionChangesService, constructionSubmissionChangesService,aviationWarSubmissionChangeService);
    }

    @Test
    public void testReceiveSubmissionSuccess() throws JsonProcessingException {
        ObjectMapper objectMapperTemp = new ObjectMapper();
        SubmissionChangeEventDto submissionChangesEvent =new SubmissionChangeEventDto();
        submissionChangesEvent.setUpdateType(UpdateType.UPDATED);
        SubmissionEvent submission=getSubmissionEvent();
        submissionChangesEvent.setPreviousSubmission(submission);
        String submissionStateChangesStringMessage = objectMapperTemp.writeValueAsString(submissionChangesEvent);
        Map<String,Object> map= new HashMap<String, Object>();
        Mockito.when(objectMapper.convertValue(Mockito.any(),Mockito.<Class<Map<String,Object>>>any())).thenReturn(map);
        Mockito.doAnswer(new Answer<Void>() {
            public Void answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                System.out.println("called with arguments: " + Arrays.toString(args));
                return null;
            }
        }).when(submissionChangesService).notifySubscribersAboutChanges(submissionChangesEvent);
        queueFacade.receiveSubmission(submissionStateChangesStringMessage);
    }

    @Test
    public void testReceiveSubmission_JsonProcessingException() throws JsonProcessingException {
        ObjectMapper objectMapperTemp = new ObjectMapper();
        SubmissionChangeEventDto submissionChangesEvent =new SubmissionChangeEventDto();
        submissionChangesEvent.setUpdateType(UpdateType.UPDATED);
        SubmissionEvent submission=getSubmissionEvent();
        submissionChangesEvent.setPreviousSubmission(submission);
        String submissionStateChangesStringMessage = objectMapperTemp.writeValueAsString(submissionChangesEvent);
        Map<String,Object> map= new HashMap<String, Object>();
        Mockito.doAnswer(new Answer<Void>() {
            public Void answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                System.out.println("called with arguments: " + Arrays.toString(args));
                return null;
            }
        }).when(submissionChangesService).notifySubscribersAboutChanges(submissionChangesEvent);
        queueFacade.receiveSubmission(TEST_STRING);
    }

    @Test
    public void testReceiveSubmission_Exception() throws JsonProcessingException {
        ObjectMapper objectMapperTemp = new ObjectMapper();
        SubmissionChangeEventDto submissionChangesEvent =new SubmissionChangeEventDto();
        submissionChangesEvent.setUpdateType(UpdateType.UPDATED);
        SubmissionEvent submission=getSubmissionEvent();
        submissionChangesEvent.setPreviousSubmission(submission);
        String submissionStateChangesStringMessage = objectMapperTemp.writeValueAsString(submissionChangesEvent);
        Map<String,Object> map= new HashMap<String, Object>();
        Mockito.when(objectMapper.convertValue(Mockito.any(),Mockito.<Class<Map<String,Object>>>any())).thenReturn(map);
        Mockito.doThrow(new RuntimeException()).when(submissionChangesService).notifySubscribersAboutChanges(Mockito.any());
        queueFacade.receiveSubmission(submissionStateChangesStringMessage);
    }

    private Submission getSubmission() {
        Submission submission = new Submission();
        submission.setId(UUID.randomUUID().toString());
        submission.setAssigneeId(TEST_STRING);
        VesselInfo vesselInfo = new VesselInfo();
        vesselInfo.setImo("8318998");
        vesselInfo.setPremium(7236.32);
        vesselInfo.setCoverFromDate(ZonedDateTime.now().toString());
        vesselInfo.setCoverToDate(ZonedDateTime.now().toString());
        vesselInfo.setProductType(TEST_STRING);
        submission.setVessels(Arrays.asList(vesselInfo));
        com.concirrus.submission.connector.submissionmanager.model.RiskInsight riskInsight = new com.concirrus.submission.connector.submissionmanager.model.RiskInsight();
        riskInsight.setExpectedLoss(32.23);
        riskInsight.setScore(3);
        riskInsight.setSeverity(4);
        riskInsight.setFrequency(3);
        submission.setRiskInsight(riskInsight);
        return submission;
    }


    private SubmissionEvent getSubmissionEvent() {
        SubmissionEvent submission = new SubmissionEvent();
        submission.setAssigneeId(TEST_STRING);
        VesselInfo vesselInfo = new VesselInfo();
        vesselInfo.setImo("8318998");
        vesselInfo.setPremium(7236.32);
        vesselInfo.setCoverFromDate(ZonedDateTime.now().toString());
        vesselInfo.setCoverToDate(ZonedDateTime.now().toString());
        vesselInfo.setProductType(TEST_STRING);
        submission.setVessels(Arrays.asList(vesselInfo));
        com.concirrus.submission.connector.submissionmanager.model.RiskInsight riskInsight = new com.concirrus.submission.connector.submissionmanager.model.RiskInsight();
        riskInsight.setExpectedLoss(32.23);
        riskInsight.setScore(3);
        riskInsight.setSeverity(4);
        riskInsight.setFrequency(3);
        submission.setRiskInsight(riskInsight);
        return submission;
    }
}
