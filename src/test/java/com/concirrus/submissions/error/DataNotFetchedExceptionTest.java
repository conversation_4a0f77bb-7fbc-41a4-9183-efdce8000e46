//package com.concirrus.submissions.error;
//
//import com.concirrus.submissions.graphql.error.DataNotFetchedException;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.Mock;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//
//
//@RunWith(SpringJUnit4ClassRunner.class)
//public class DataNotFetchedExceptionTest {
//    @Mock
//    DataNotFetchedException dataNotFetchedException;
//
//    private static final String TEST_MOCK_STRING = "test";
//
//    @Before
//    public void setUp() {
//        dataNotFetchedException = new DataNotFetchedException(TEST_MOCK_STRING);
//    }
//
//}
