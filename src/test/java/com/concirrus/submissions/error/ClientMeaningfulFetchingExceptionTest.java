package com.concirrus.submissions.error;

import com.concirrus.submissions.graphql.error.ClientMeaningfulFetchingException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.HashMap;

@RunWith(SpringJUnit4ClassRunner.class)
public class ClientMeaningfulFetchingExceptionTest {

    @Mock
    ClientMeaningfulFetchingException clientMeaningfulFetchingException;

    @Mock
    ClientMeaningfulFetchingException clientMeaningfulFetchingException2;


    private static final String TEST_MOCK_STRING = "test";

    @Before
    public void setUp() {
        clientMeaningfulFetchingException = new ClientMeaningfulFetchingException(TEST_MOCK_STRING,TEST_MOCK_STRING,new HashMap<String,Object>());
        clientMeaningfulFetchingException2 = new ClientMeaningfulFetchingException(TEST_MOCK_STRING,TEST_MOCK_STRING);
    }

    @Test
    public void testGetErrorType()
    {
        clientMeaningfulFetchingException2.getErrorType();
    }

    @Test
    public void testGetExtensions()
    {
        clientMeaningfulFetchingException.getExtensions();
    }

}
