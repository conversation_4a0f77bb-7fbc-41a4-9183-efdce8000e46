package com.concirrus.submissions.error;

import com.concirrus.submissions.graphql.error.DefaultGraphQLError;
import graphql.ErrorClassification;
import graphql.language.SourceLocation;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.HashMap;

@RunWith(SpringJUnit4ClassRunner.class)
public class DefaultGraphQLErrorTest {

    @Mock
    DefaultGraphQLError defaultGraphQLError;

    private static final String TEST_MOCK_STRING = "test";

    @Mock
    ErrorClassification errorClassification;

    @Before
    public void setUp() {
        defaultGraphQLError = new DefaultGraphQLError(TEST_MOCK_STRING, new ArrayList<SourceLocation>(), errorClassification, new ArrayList<>(), new HashMap<String, Object>());
    }

    @Test
    public void testGetMessage() {

        defaultGraphQLError.getMessage();
    }

    @Test
    public void testGetLocations() {
        defaultGraphQLError.getLocations();
    }

    @Test
    public void testGetErrorType() {
        defaultGraphQLError.getErrorType();
    }

    @Test
    public void testGetPath() {
        defaultGraphQLError.getPath();
    }

    @Test
    public void testGetExtensions() {
        defaultGraphQLError.getExtensions();
    }
}
