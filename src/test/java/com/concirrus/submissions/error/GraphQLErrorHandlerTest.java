package com.concirrus.submissions.error;

import com.concirrus.submissions.graphql.error.DataNotFetchedException;
import com.concirrus.submissions.graphql.error.DefaultGraphQLError;
import com.concirrus.submissions.graphql.error.GraphQLErrorHandler;
import graphql.ErrorType;
import graphql.GraphQLError;
import graphql.language.SourceLocation;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
public class GraphQLErrorHandlerTest {

    @Mock
    GraphQLErrorHandler graphQLErrorHandler;

    @Before
    public void setUp() {
        graphQLErrorHandler = new GraphQLErrorHandler();
    }

    private static final String TEST_MOCK_STRING = "test";


    @Test
    public void testProcessErrorsSuccess()
    {
        GraphQLErrorHandler graphQLErrorHandlerSpy = Mockito.spy(graphQLErrorHandler);
        Mockito.when(graphQLErrorHandlerSpy.invokeMethod(new DataNotFetchedException(TEST_MOCK_STRING) {
        }, "getMessage")).thenReturn(TEST_MOCK_STRING);
        Mockito.when(graphQLErrorHandlerSpy.invokeMethod(new DataNotFetchedException(TEST_MOCK_STRING) {
        }, "getException")).thenReturn(new Object());
        List<SourceLocation> locations = new ArrayList<>();
        SourceLocation sourceLocation = new SourceLocation(12,12);
        locations.add(sourceLocation);
        GraphQLError error= new DefaultGraphQLError(TEST_MOCK_STRING, locations, ErrorType.DataFetchingException, new ArrayList<>(), new HashMap<String, Object> ());
        List<GraphQLError> errors = new ArrayList<>();
        errors.add(error);
        List<GraphQLError> response=  graphQLErrorHandlerSpy.processErrors(errors);
    }

    @Test
    public void testProcessErrorsEmptyList()
    {
        GraphQLErrorHandler graphQLErrorHandlerSpy = Mockito.spy(graphQLErrorHandler);
        Mockito.when(graphQLErrorHandlerSpy.invokeMethod(new DataNotFetchedException(TEST_MOCK_STRING){
        }, "getMessage")).thenReturn(TEST_MOCK_STRING);
        Mockito.when(graphQLErrorHandlerSpy.invokeMethod(new DataNotFetchedException(TEST_MOCK_STRING) {
        }, "getException")).thenReturn(new Object());
        List<SourceLocation> locations = new ArrayList<>();
        SourceLocation sourceLocation = new SourceLocation(12,12);
        locations.add(sourceLocation);
        GraphQLError error= new DefaultGraphQLError(TEST_MOCK_STRING, locations, ErrorType.DataFetchingException, new ArrayList<>(), new HashMap<String, Object> ());
        List<GraphQLError> errors = new ArrayList<>();
        List<GraphQLError> response=  graphQLErrorHandlerSpy.processErrors(errors);
    }


    @Test
    public void testGetField()
    {
        List<SourceLocation> locations = new ArrayList<>();
        SourceLocation sourceLocation = new SourceLocation(12,12);
        DefaultGraphQLError error= new DefaultGraphQLError(TEST_MOCK_STRING, locations, ErrorType.DataFetchingException, new ArrayList<>(), new HashMap<String, Object> ());
        graphQLErrorHandler.getField(error,"message");
    }


    @Test
    public void testGetFieldException()
    {
        List<SourceLocation> locations = new ArrayList<>();
        SourceLocation sourceLocation = new SourceLocation(12,12);
        DefaultGraphQLError error= new DefaultGraphQLError(TEST_MOCK_STRING, locations, ErrorType.DataFetchingException, new ArrayList<>(), new HashMap<String, Object> ());
        graphQLErrorHandler.getField(error,TEST_MOCK_STRING);
    }

}
