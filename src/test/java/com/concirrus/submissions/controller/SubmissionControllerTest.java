package com.concirrus.submissions.controller;

import com.concirrus.quest.common.rest.model.BasicResponse;
import com.concirrus.submission.connector.submissionmanager.SubmissionManagerConnector;
import com.concirrus.submission.connector.submissionmanager.model.AdditionalInfo;
import com.concirrus.submissions.common.CommonConstants;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.servlet.http.HttpServletRequest;
import java.util.UUID;

@RunWith(SpringJUnit4ClassRunner.class)
public class SubmissionControllerTest {

    @Mock
     SubmissionManagerConnector submissionManagerConnector;

    @Mock
    SubmissionController submissionController;

    @Mock
    HttpServletRequest httpServletRequest;

    private static final String TEST_STRING = "test";

    @Before
    public void setUp() {
        submissionController = new SubmissionController(submissionManagerConnector);
    }

    @Test
    public void getAdditionalInfoSuccess() {
        BasicResponse<AdditionalInfo> additionalInfoBasicResponse = new BasicResponse().setStatus(200).setResult(new AdditionalInfo());
        Mockito.when(submissionManagerConnector.getAdditionalInfo(Mockito.any(),Mockito.any(),Mockito.anyBoolean())).thenReturn(additionalInfoBasicResponse);
        Mockito.when(httpServletRequest.getHeader(CommonConstants.CLIENT_ID_HEADER)).thenReturn(UUID.randomUUID().toString());
        ResponseEntity<BasicResponse<AdditionalInfo>> response= submissionController.getAdditionalInfo(UUID.randomUUID().toString(),Boolean.FALSE,httpServletRequest);
        Assert.assertEquals(HttpStatus.OK.value(),response.getBody().getStatus());
    }

    @Test
    public void getAdditionalInfoError() {
        BasicResponse<AdditionalInfo> additionalInfoBasicResponse = new BasicResponse().setStatus(404).setError(TEST_STRING);
        Mockito.when(httpServletRequest.getHeader(CommonConstants.CLIENT_ID_HEADER)).thenReturn(UUID.randomUUID().toString());
        Mockito.when(submissionManagerConnector.getAdditionalInfo(Mockito.any(),Mockito.any(),Mockito.anyBoolean())).thenReturn(additionalInfoBasicResponse);
        ResponseEntity<BasicResponse<AdditionalInfo>> response= submissionController.getAdditionalInfo(UUID.randomUUID().toString(),Boolean.FALSE,httpServletRequest);
        Assert.assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.value(),response.getBody().getStatus());
    }
}
