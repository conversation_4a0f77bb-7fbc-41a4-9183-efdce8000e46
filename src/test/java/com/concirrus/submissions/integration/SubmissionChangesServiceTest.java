//package com.concirrus.submissions.integration;
//
//import com.concirrus.submission.connector.submissionmanager.SubmissionManagerConnector;
//import com.concirrus.submissions.graphql.resolvers.Query;
//import com.concirrus.submissions.model.SubmissionStateChanges;
//import com.concirrus.submissions.model.SubmissionsStateChange;
//import com.concirrus.submissions.service.mapper.SubmissionMapper;
//import org.junit.Before;
//import org.mockito.Mock;
//import reactor.core.publisher.Flux;
//
//import java.util.Map;
//
//public class SubmissionChangesServiceTest {
//
//    @Mock
//    SubmissionManagerConnector submissionManagerConnector;
//    @Mock
//    AccessManagementSal accessManagementSal;
//    @Mock
//    Query query;
//    @Mock
//    Map<String, Flux<Long>> clientSubmissionsInboxItemCount;
//    @Mock
//    Map<String, Flux<Long>> clientSubmissionsInReviewItemCount;
//    @Mock
//    Map<String, Flux<Long>> clientSubmissionsQuotedItemCount ;
//    @Mock
//    Map<String, Flux<Long>> clientSubmissionsDoneItemCount ;
//    @Mock
//    Map<String,Flux<SubmissionsStateChange>> clientSubmissionStateChange ;
//    @Mock
//    SubmissionChangesService.PublisherContainer<SubmissionStateChanges> submissionChangesPublisherContainer;
//    @Mock
//    SubmissionChangesService submissionChangesService;
//    @Before
//    public void setUp() {
//
//        submissionChangesService = new SubmissionChangesService(submissionManagerConnector,accessManagementSal,query,clientSubmissionsInboxItemCount,clientSubmissionsInReviewItemCount,clientSubmissionsQuotedItemCount,clientSubmissionsDoneItemCount,clientSubmissionStateChange,submissionChangesPublisherContainer);
//    }
//
//}
