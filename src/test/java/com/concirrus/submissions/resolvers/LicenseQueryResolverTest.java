package com.concirrus.submissions.resolvers;

import com.concirrus.submissions.common.CommonUtil;
import com.concirrus.submissions.graphql.model.LicenceDetail;
import com.concirrus.submissions.graphql.model.LicenceQuery;
import com.concirrus.submissions.graphql.resolvers.LicenceQueryResolver;
import com.concirrus.submissions.model.repository.LicenceHash;
import com.concirrus.submissions.model.repository.enums.LicenceType;
import com.concirrus.submissions.repository.LicenceHashRepository;
import graphql.GraphQLException;
import graphql.schema.DataFetchingEnvironment;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.time.LocalDate;
import java.util.Optional;
import java.util.UUID;

@RunWith(SpringJUnit4ClassRunner.class)
public class LicenseQueryResolverTest {
    @Mock
    LicenceHashRepository licenceHashRepository;

    @Mock
    CommonUtil commonUtil;

    @Mock
    LicenceQueryResolver licenseQueryResolver;

    @Mock
    DataFetchingEnvironment dataFetchingEnvironment;

    private static String TEST_MOCK_STRING = "test";


    @Before
    public void setUp() {
        licenseQueryResolver = new LicenceQueryResolver(licenceHashRepository,commonUtil);
    }


    @Test(expected = GraphQLException.class)
    public void testLicenceDetailException()
    {
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(licenceHashRepository.findById(Mockito.anyString())).thenReturn(Optional.empty());
        licenseQueryResolver.licenceDetail(new LicenceQuery(),dataFetchingEnvironment);
    }

    @Test
    public void testLicenceDetailSuccess()
    {
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        LicenceHash licenceHash=new LicenceHash();
        licenceHash.setClientId(TEST_MOCK_STRING);
        licenceHash.setLicenceType(LicenceType.NORMAL);
        licenceHash.setValidFrom(LocalDate.now());
        licenceHash.setValidFrom(LocalDate.now());
        Mockito.when(licenceHashRepository.findById(Mockito.anyString())).thenReturn(Optional.of(licenceHash));
        LicenceDetail licenceDetail =licenseQueryResolver.licenceDetail(new LicenceQuery(),dataFetchingEnvironment);
        Assert.assertNotNull(licenceDetail);
    }
}
