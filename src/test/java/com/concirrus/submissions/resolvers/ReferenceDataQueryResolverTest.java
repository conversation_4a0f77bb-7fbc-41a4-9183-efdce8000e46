package com.concirrus.submissions.resolvers;

import com.concirrus.quest.common.rest.model.BasicResponse;
import com.concirrus.submission.connector.workflowmanager.WorkflowManagerConnector;
import com.concirrus.submission.connector.workflowmanager.model.SubmissionStateResponse;
import com.concirrus.submission.connector.workflowmanager.model.SubmissionStatusResponse;
import com.concirrus.submissions.common.CommonUtil;
import com.concirrus.submissions.connector.questinsights.QuestInsightsConnector;
import com.concirrus.submissions.connector.questinsights.model.referencedata.Product;
import com.concirrus.submissions.graphql.model.ReferenceData;
import com.concirrus.submissions.graphql.resolvers.ReferenceDataQueryResolver;
import graphql.GraphQLException;
import graphql.schema.DataFetchingEnvironment;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@RunWith(SpringJUnit4ClassRunner.class)
public class ReferenceDataQueryResolverTest {

    @Mock
    QuestInsightsConnector questInsightsConnector ;

    @Mock
    WorkflowManagerConnector workflowManagerConnector;

    @Mock
    CommonUtil commonUtil;

    @Mock
    ReferenceDataQueryResolver referenceDataQueryResolver;

    @Mock
    ReferenceData referenceData;

    @Mock
    DataFetchingEnvironment dataFetchingEnvironment;

    private static final String TEST_MOCK_STRING = "test";

    @Before
    public void setUp() {
        referenceDataQueryResolver = new ReferenceDataQueryResolver(questInsightsConnector, workflowManagerConnector, commonUtil);
    }

    @Test
    public void testSubmissionStatesSuccess()
    {
        BasicResponse<List<SubmissionStateResponse>> response = new BasicResponse().setResult(new ArrayList<>()).setStatus(200);
        Mockito.when(workflowManagerConnector.getAllSubmissionStates()).thenReturn(response);
        referenceDataQueryResolver.submissionStates(referenceData,dataFetchingEnvironment);
    }

    @Test(expected = GraphQLException.class)
    public void testSubmissionStatesError()
    {
        BasicResponse<List<SubmissionStateResponse>> response = new BasicResponse().setError("Error").setStatus(404);
        Mockito.when(workflowManagerConnector.getAllSubmissionStates()).thenReturn(response);
        referenceDataQueryResolver.submissionStates(referenceData,dataFetchingEnvironment);
    }

    @Test
    public void testSubmissionStatusSuccess()
    {
        BasicResponse<List<SubmissionStatusResponse>> response = new BasicResponse().setResult(new ArrayList<>()).setStatus(200);
        Mockito.when(workflowManagerConnector.getAllSubmissionStatuses()).thenReturn(response);
        referenceDataQueryResolver.submissionStatus(referenceData,dataFetchingEnvironment);
    }

    @Test(expected = GraphQLException.class)
    public void testSubmissionStatusError()
    {
        BasicResponse<List<SubmissionStatusResponse>> response = new BasicResponse().setError("Error").setStatus(404);
        Mockito.when(workflowManagerConnector.getAllSubmissionStatuses()).thenReturn(response);
        referenceDataQueryResolver.submissionStatus(referenceData,dataFetchingEnvironment);
    }


    @Test
    public void testStatusCommentsSuccess()
    {
        BasicResponse<List<String>> response = new BasicResponse().setResult(new ArrayList<>()).setStatus(200);
        Mockito.when(workflowManagerConnector.getSubmissionRejectionReasons(Mockito.anyString())).thenReturn(response);
        referenceDataQueryResolver.statusComments(referenceData,TEST_MOCK_STRING,dataFetchingEnvironment);
    }

    @Test(expected = GraphQLException.class)
    public void testStatusCommentsError()
    {
        BasicResponse<List<String>> response = new BasicResponse().setError("Error").setStatus(404);
        Mockito.when(workflowManagerConnector.getSubmissionRejectionReasons(Mockito.anyString())).thenReturn(response);
        referenceDataQueryResolver.statusComments(referenceData,TEST_MOCK_STRING,dataFetchingEnvironment);
    }

    @Test
    public void testProductsSuccess()
    {
        BasicResponse<List<Product>> response = new BasicResponse().setResult(new ArrayList<>()).setStatus(200);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(questInsightsConnector.getProducts(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(response);
        referenceDataQueryResolver.products(referenceData,TEST_MOCK_STRING,dataFetchingEnvironment);
    }

    @Test(expected = GraphQLException.class)
    public void testProductsError()
    {
        BasicResponse<List<Product>> response = new BasicResponse().setError("Error").setStatus(404);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(questInsightsConnector.getProducts(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(response);
        referenceDataQueryResolver.products(referenceData,TEST_MOCK_STRING,dataFetchingEnvironment);
    }

    @Test
    public void testRiskScores()
    {
        referenceDataQueryResolver.riskScores(referenceData,dataFetchingEnvironment);
    }

    @Test
    public void testFleetSizes()
    {
        referenceDataQueryResolver.fleetSizes(referenceData,dataFetchingEnvironment);
    }
}
