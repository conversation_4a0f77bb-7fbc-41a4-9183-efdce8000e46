package com.concirrus.submissions.resolvers;

import com.concirrus.submission.connector.submissionmanager.model.SubmissionState;
import com.concirrus.submissions.graphql.resolvers.Subscription;
import com.concirrus.submissions.integration.AviationSubmissionChangesService;
import com.concirrus.submissions.integration.AviationWarSubmissionChangeService;
import com.concirrus.submissions.integration.ConstructionSubmissionChangesService;
import com.concirrus.submissions.integration.SubmissionChangesService;
import com.concirrus.submissions.model.SubmissionsChange;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import reactor.core.publisher.Flux;

import java.util.HashMap;
import java.util.Map;

@RunWith(SpringJUnit4ClassRunner.class)
public class SubscriptionTest {

    private static String TEST_MOCK_STRING = "test";
    @Mock
    SubmissionChangesService submissionChangesService;
    @Mock
    AviationSubmissionChangesService aviationSubmissionChangesService;
    @Mock
    ConstructionSubmissionChangesService constructionSubmissionChangesService;
    @Mock
    AviationWarSubmissionChangeService aviationWarSubmissionChangeService;
    @Mock
    Subscription subscription;

    @Before
    public void setUp() {
        subscription = new Subscription(submissionChangesService, aviationSubmissionChangesService, constructionSubmissionChangesService, aviationWarSubmissionChangeService);
    }


    @Test
    public  void testSubmissionsItemCount()
    {
        Map<String, Flux<Long>> countMap = new HashMap<>();
        Mockito.when(submissionChangesService.getSubmissionItemCountPublisher(Mockito.anyString(),Mockito.any(String.class))).thenReturn(countMap.get(SubmissionState.DONE));
        subscription.submissionsItemCount(TEST_MOCK_STRING,SubmissionState.DONE.name(), null);
    }

    @Test
    public  void testSubmissionsStateChange()
    {
        Map<String, Flux<SubmissionsChange>> map = new HashMap<>();
        Mockito.when(submissionChangesService.getSubmissionChanges(Mockito.anyString())).thenReturn(map.get(TEST_MOCK_STRING));
        subscription.submissionChanges(TEST_MOCK_STRING);
    }


}
