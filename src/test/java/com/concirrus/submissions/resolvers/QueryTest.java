

package com.concirrus.submissions.resolvers;

import com.concirrus.quest.common.rest.model.BasicResponse;
import com.concirrus.submission.connector.accessmanagement.model.UserInfoResponse;
import com.concirrus.submission.connector.policyintegration.PolicyIntegrationConnector;
import com.concirrus.submission.connector.policyintegration.model.PolicyStatus;
import com.concirrus.submission.connector.policyintegration.model.PolicySummary;
import com.concirrus.submission.connector.policyintegration.model.PolicyType;
import com.concirrus.submission.connector.submissionmanager.SubmissionManagerConnector;
import com.concirrus.submission.connector.submissionmanager.model.Submission;
import com.concirrus.submission.connector.submissionmanager.model.SubmissionDetailResponse;
import com.concirrus.submission.connector.submissionmanager.model.SubmissionState;
import com.concirrus.submission.connector.submissionmanager.model.VesselInfo;
import com.concirrus.submission.service.notes.connector.notes.NotesServiceConnector;
import com.concirrus.submissions.common.CommonUtil;
import com.concirrus.submissions.connector.questinsights.QuestInsightsConnector;
import com.concirrus.submissions.connector.questinsights.model.referencedata.Product;
import com.concirrus.submissions.connector.questinsights.model.user.UserInfo;
import com.concirrus.submissions.connector.questinsights.model.vessels.RiskScore;
import com.concirrus.submissions.graphql.model.LicenceQuery;
import com.concirrus.submissions.graphql.model.ReferenceData;
import com.concirrus.submissions.graphql.resolvers.Query;
import com.concirrus.submissions.integration.AccessManagementSal;
import com.concirrus.submissions.model.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import graphql.GraphQLException;
import graphql.schema.DataFetchingEnvironment;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.*;

@RunWith(SpringJUnit4ClassRunner.class)
public class QueryTest {
    private static final Logger logger = LoggerFactory.getLogger(Query.class);

    @Mock
    SubmissionManagerConnector submissionManagerConnector;

    @Mock
    QuestInsightsConnector questInsightsConnector;

    @Mock
    ObjectMapper objectMapper;

    @Mock
    CommonUtil commonUtil;

    @Mock
    AccessManagementSal accessManagementSal;

    @Mock
    Query query;

    public ReferenceData referenceData() {
        return new ReferenceData();
    }

    public LicenceQuery licence() {
        return new LicenceQuery();
    }

    private static String TEST_MOCK_STRING = "test";

    @Mock
    DataFetchingEnvironment dataFetchingEnvironment;

    @Mock
    NotesServiceConnector notesServiceConnector;

    @Mock
    PolicyIntegrationConnector policyIntegrationConnector;

    @Before
    public void setUp() {
        objectMapper = new ObjectMapper();
        query = new Query(submissionManagerConnector,objectMapper,commonUtil,notesServiceConnector,policyIntegrationConnector,questInsightsConnector,accessManagementSal);
    }

    @Test
    public void testUsersEmptyList()
    {
        Query querySpy = Mockito.spy(new Query(submissionManagerConnector,objectMapper,commonUtil,notesServiceConnector,policyIntegrationConnector,questInsightsConnector,accessManagementSal));
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        BasicResponse<List< UserInfo >> userInfoResponse = new BasicResponse().setStatus(200).setResult(new ArrayList<>());
        Map<String, User> userMap=new HashMap<>() ;
        Mockito.doReturn(userMap).when(querySpy).getAllUsersMap(TEST_MOCK_STRING,TEST_MOCK_STRING);
        Mockito.when(questInsightsConnector.getUsers(Mockito.anyString(),Mockito.anyString())).thenReturn(userInfoResponse);
        List<User> users= querySpy.users(Arrays.asList(TEST_MOCK_STRING),dataFetchingEnvironment);
        Assert.assertEquals(0,users.size());
    }

    @Test
    public void testUsersSuccess()
    {
        Query querySpy = Mockito.spy(query);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Map<String, User> userMap=new HashMap<>() ;
        userMap.put(TEST_MOCK_STRING,new User());
        UserInfo userInfo= new UserInfo();
        userInfo.setUserId(TEST_MOCK_STRING);
        BasicResponse<List<UserInfo>> userInfoResponse = new BasicResponse().setStatus(200).setResult(Arrays.asList(userInfo));
        Mockito.doReturn(userMap).when(querySpy).getAllUsersMap(TEST_MOCK_STRING,TEST_MOCK_STRING);
        Mockito.when(questInsightsConnector.getUsers(Mockito.anyString(),Mockito.anyString())).thenReturn(userInfoResponse);
        List<User> users= querySpy.users(Arrays.asList(TEST_MOCK_STRING),dataFetchingEnvironment);
        Assert.assertEquals(userMap.size(),users.size());
    }

    @Test
    public void testUsersQuestInsightsReturnNull()
    {
        Query querySpy = Mockito.spy(query);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Map<String, User> userMap=new HashMap<>() ;
        userMap.put(TEST_MOCK_STRING,new User());
        UserInfo userInfo= new UserInfo();
        userInfo.setUserId(TEST_MOCK_STRING);
        BasicResponse<List<UserInfo>> userInfoResponse = new BasicResponse().setStatus(200).setResult(Arrays.asList(userInfo));
        Mockito.doReturn(userMap).when(querySpy).getAllUsersMap(TEST_MOCK_STRING,TEST_MOCK_STRING);
        Mockito.when(questInsightsConnector.getUsers(Mockito.anyString(),Mockito.anyString())).thenReturn(null);
        List<User> users= querySpy.users(Arrays.asList(TEST_MOCK_STRING),dataFetchingEnvironment);
        Assert.assertEquals(0,users.size());
    }

    @Test
    public void testUsersQuestInsightsReturnError()
    {
        Query querySpy = Mockito.spy(query);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Map<String, User> userMap=new HashMap<>() ;
        userMap.put(TEST_MOCK_STRING,new User());
        UserInfo userInfo= new UserInfo();
        userInfo.setUserId(TEST_MOCK_STRING);
        BasicResponse<List<UserInfo>> userInfoResponse = new BasicResponse().setStatus(400).setError("Error").setResult(Arrays.asList(userInfo));
        Mockito.doReturn(userMap).when(querySpy).getAllUsersMap(TEST_MOCK_STRING,TEST_MOCK_STRING);
        Mockito.when(questInsightsConnector.getUsers(Mockito.anyString(),Mockito.anyString())).thenReturn(userInfoResponse);
        List<User> users= querySpy.users(Arrays.asList(TEST_MOCK_STRING),dataFetchingEnvironment);
        Assert.assertEquals(0,users.size());
    }

    @Test
    public void testUsersQuestInsightsUserIds()
    {
        Query querySpy = Mockito.spy(query);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Map<String, User> userMap=new HashMap<>() ;
        userMap.put(TEST_MOCK_STRING,new User());
        UserInfo userInfo= new UserInfo();
        userInfo.setUserId(TEST_MOCK_STRING);
        BasicResponse<List<UserInfo>> userInfoResponse = new BasicResponse().setStatus(200).setResult(Arrays.asList(userInfo));
        Mockito.doReturn(userMap).when(querySpy).getAllUsersMap(TEST_MOCK_STRING,TEST_MOCK_STRING);
        Mockito.when(questInsightsConnector.getUsers(Mockito.anyString(),Mockito.anyString())).thenReturn(userInfoResponse);
        List<User> users= querySpy.users(new ArrayList<>(),dataFetchingEnvironment);
        Assert.assertEquals(userInfoResponse.getResult().size(),users.size());
    }


    @Test
    public void testCountSubmissionsByStateTestSuccess()
    {
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Long value = 2l;
        BasicResponse<Long> response = new BasicResponse().setStatus(200).setResult(value);
        Mockito.when(submissionManagerConnector.getSubmissionCountByState(Mockito.anyString(), Mockito.any(), Mockito.anyBoolean())).thenReturn(response);
        Long count= query.countSubmissionsByState(SubmissionState.DONE, Boolean.FALSE, dataFetchingEnvironment);
    }


    @Test(expected = GraphQLException.class)
    public void testCountSubmissionsByStateTestError()
    {
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        BasicResponse<Long> response = new BasicResponse().setStatus(400).setError("Error");
        Mockito.when(submissionManagerConnector.getSubmissionCountByState(Mockito.anyString(), Mockito.any(), Mockito.anyBoolean())).thenReturn(response);
        query.countSubmissionsByState(SubmissionState.DONE, Boolean.FALSE, dataFetchingEnvironment);
    }


    @Test
    public void testSubmissionsByIds()
    {
        Query querySpy = Mockito.spy(query);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        BasicResponse<List<SubmissionDetailResponse>> response = new BasicResponse().setStatus(200).setResult(new ArrayList<>());
        Mockito.when(submissionManagerConnector.getSubmissionsByIds(Mockito.anyList(),Mockito.anyString(),Mockito.anyBoolean())).thenReturn(response);
        Map<String, User> userMap=new HashMap<>() ;
        userMap.put(TEST_MOCK_STRING,new User());
        UserInfo userInfo= new UserInfo();
        userInfo.setUserId(TEST_MOCK_STRING);
        Mockito.doReturn(userMap).when(querySpy).getAllUsersMap(Mockito.anyString(),Mockito.anyString());
        List<com.concirrus.submissions.graphql.model.Submission> detail =querySpy.submissionsByIds(Arrays.asList(TEST_MOCK_STRING),TEST_MOCK_STRING,Boolean.FALSE,TEST_MOCK_STRING,dataFetchingEnvironment);
        Assert.assertEquals(true,CollectionUtils.isEmpty(detail));
    }

    @Test(expected = GraphQLException.class)
    public void testSubmissionsByIdsGraphQlException()
    {
        Query querySpy = Mockito.spy(query);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        BasicResponse<List<SubmissionDetailResponse>> response = new BasicResponse().setStatus(400).setError("Error");
        Mockito.when(submissionManagerConnector.getSubmissionsByIds(Mockito.anyList(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(response);
        Map<String, User> userMap = new HashMap<>();
        Mockito.doReturn(userMap).when(querySpy).getAllUsersMap(Mockito.anyString(),Mockito.anyString());
        List<com.concirrus.submissions.graphql.model.Submission> detail =querySpy.submissionsByIds(Arrays.asList(TEST_MOCK_STRING),TEST_MOCK_STRING,Boolean.FALSE,TEST_MOCK_STRING,dataFetchingEnvironment);
    }


    @Test
    public void testSubmissionsByIdsSuccess()
    {
        Query querySpy = Mockito.spy(query);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        BasicResponse<List<SubmissionDetailResponse>> response = new BasicResponse().setStatus(200).setResult(new ArrayList<>());
        Mockito.when(submissionManagerConnector.getSubmissionsByIds(Mockito.anyList(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(response);
        Map<String, User> userMap = new HashMap<>();
        Mockito.doReturn(userMap).when(querySpy).getAllUsersMap(Mockito.anyString(),Mockito.anyString());
        List<com.concirrus.submissions.graphql.model.Submission> detail =querySpy.submissionsByIds(Arrays.asList(TEST_MOCK_STRING),TEST_MOCK_STRING,Boolean.FALSE,TEST_MOCK_STRING,dataFetchingEnvironment);
        Assert.assertEquals(true,CollectionUtils.isEmpty(detail));
    }

    @Test
    public void testSubmissionsByIdsVesselListEmpty()
    {
        Query querySpy = Mockito.spy(query);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        SubmissionDetailResponse submission = new SubmissionDetailResponse();
        submission.setId(UUID.randomUUID().toString());
        submission.setAssigneeId(TEST_MOCK_STRING);
        BasicResponse<List<SubmissionDetailResponse>> response = new BasicResponse().setStatus(200).setResult(Arrays.asList(submission));
        Mockito.when(submissionManagerConnector.getSubmissionsByIds(Mockito.anyList(),Mockito.anyString(),Mockito.anyBoolean())).thenReturn(response);
        Map<String, User> userMap = new HashMap<>();
        Mockito.doReturn(userMap).when(querySpy).getAllUsersMap(Mockito.anyString(),Mockito.anyString());
        List<com.concirrus.submissions.graphql.model.Submission> detail =querySpy.submissionsByIds(Arrays.asList(TEST_MOCK_STRING),TEST_MOCK_STRING,Boolean.FALSE,TEST_MOCK_STRING,dataFetchingEnvironment);
        Assert.assertEquals(false,CollectionUtils.isEmpty(detail));
    }

//    @Test
    public void testSubmissionsByIdsVesselsListNotEmptySuccess()
    {
        Query querySpy = Mockito.spy(query);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        SubmissionDetailResponse submission = new SubmissionDetailResponse();
        submission.setId(UUID.randomUUID().toString());
        submission.setAssigneeId(TEST_MOCK_STRING);
        VesselInfo vesselInfo = new VesselInfo();
        vesselInfo.setImo("8318998");
        vesselInfo.setPremium(7236.32);
        vesselInfo.setCoverFromDate(ZonedDateTime.now().toString());
        vesselInfo.setCoverToDate(ZonedDateTime.now().toString());
        vesselInfo.setProductType(TEST_MOCK_STRING);
        submission.setVessels(Arrays.asList(vesselInfo));
        BasicResponse<List<SubmissionDetailResponse>> response = new BasicResponse().setStatus(200).setResult(Arrays.asList(submission));
        Mockito.when(submissionManagerConnector.getSubmissionsByIds(Mockito.anyList(),Mockito.anyString(),Mockito.anyBoolean())).thenReturn(response);
        Map<String, User> userMap = new HashMap<>();
        Mockito.doReturn(userMap).when(querySpy).getAllUsersMap(Mockito.anyString(),Mockito.anyString());
        RiskScore riskScore = new RiskScore();
        riskScore.setScore(3);
        riskScore.setExpectedLoss(32.32);
        riskScore.setFrequencyScore(8);
        riskScore.setSeverityScore(3);
        BasicResponse<RiskScore> riskScoreResponse = new BasicResponse().setStatus(200).setResult(riskScore);
        Mockito.when(questInsightsConnector.getRiskScores(Mockito.anyString(), Mockito.anyString(), Mockito.anyList(), Mockito.anyString(), Mockito.anyString())).thenReturn(riskScoreResponse);
        List<com.concirrus.submissions.graphql.model.Submission> detail =querySpy.submissionsByIds(Arrays.asList(TEST_MOCK_STRING),TEST_MOCK_STRING,Boolean.FALSE,TEST_MOCK_STRING,dataFetchingEnvironment);

    }

//    @Test
    public void testSubmissionsByIdsVesselsListNotEmptyError()
    {
        Query querySpy = Mockito.spy(query);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        SubmissionDetailResponse submission = new SubmissionDetailResponse();
        submission.setId(UUID.randomUUID().toString());
        submission.setAssigneeId(TEST_MOCK_STRING);
        VesselInfo vesselInfo = new VesselInfo();
        vesselInfo.setImo("8318998");
        vesselInfo.setPremium(7236.32);
        vesselInfo.setCoverFromDate(ZonedDateTime.now().toString());
        vesselInfo.setCoverToDate(ZonedDateTime.now().toString());
        vesselInfo.setProductType(TEST_MOCK_STRING);
        submission.setVessels(Arrays.asList(vesselInfo));
        BasicResponse<List<SubmissionDetailResponse>> response = new BasicResponse().setStatus(200).setResult(Arrays.asList(submission));
        Mockito.when(submissionManagerConnector.getSubmissionsByIds(Mockito.anyList(),Mockito.anyString(),Mockito.anyBoolean())).thenReturn(response);
        Map<String, User> userMap = new HashMap<>();
        Mockito.doReturn(userMap).when(querySpy).getAllUsersMap(Mockito.anyString(),Mockito.anyString());
        RiskScore riskScore = new RiskScore();
        riskScore.setScore(3);
        riskScore.setExpectedLoss(32.32);
        riskScore.setFrequencyScore(8);
        riskScore.setSeverityScore(3);
        BasicResponse<RiskScore> riskScoreResponse = new BasicResponse().setStatus(404).setError("Error");
        Mockito.when(questInsightsConnector.getRiskScores(Mockito.anyString(), Mockito.anyString(), Mockito.anyList(), Mockito.anyString(), Mockito.anyString())).thenReturn(riskScoreResponse);
        List<com.concirrus.submissions.graphql.model.Submission> detail =querySpy.submissionsByIds(Arrays.asList(TEST_MOCK_STRING),TEST_MOCK_STRING,Boolean.FALSE,TEST_MOCK_STRING,dataFetchingEnvironment);
    }


    @Test
    public void testSubmissionsSuccessEmptyList()
    {
        Query querySpy = Mockito.spy(query);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        BasicResponse<List<Submission>> response = new BasicResponse().setStatus(200).setResult(new ArrayList<>());
        Mockito.when(submissionManagerConnector.getAllSubmissionsByStateAndDateRange(Mockito.any(Instant.class), Mockito.any(Instant.class), Mockito.any(SubmissionState.class), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt(), Mockito.any(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(response);
        Map<String, User> userMap = new HashMap<>();
        Mockito.doReturn(userMap).when(querySpy).getAllUsersMap(Mockito.anyString(),Mockito.anyString());
        Page page = new Page();
        page.setPageIndex(0);
        page.setPageSize(10);
        Sort sort = new Sort();
        sort.setSortBy("state");
        sort.setSortOrder(SortOrder.ASC);
        List<SubmissionDetail>submissionDetails= querySpy.submissions(Boolean.FALSE,LocalDate.now(),LocalDate.now(),SubmissionState.DONE.toString(),TEST_MOCK_STRING,TEST_MOCK_STRING,page,sort,dataFetchingEnvironment);
        Assert.assertEquals(true,CollectionUtils.isEmpty(submissionDetails));
    }

    @Test(expected = GraphQLException.class)
    public void testSubmissionsSuccessGetFromSubmissionManagerError()
    {
        Query querySpy = Mockito.spy(query);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        BasicResponse<List<Submission>> response = new BasicResponse().setStatus(400).setError("Error");
        Mockito.when(submissionManagerConnector.getAllSubmissionsByStateAndDateRange(Mockito.any(Instant.class), Mockito.any(Instant.class), Mockito.any(SubmissionState.class), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt(), Mockito.any(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(response);
        Map<String, User> userMap = new HashMap<>();
        Mockito.doReturn(userMap).when(querySpy).getAllUsersMap(Mockito.anyString(),Mockito.anyString());
        Page page = new Page();
        page.setPageIndex(0);
        page.setPageSize(10);
        Sort sort = new Sort();
        sort.setSortBy("state");
        sort.setSortOrder(SortOrder.ASC);
        List<SubmissionDetail>submissionDetails= querySpy.submissions(Boolean.FALSE,LocalDate.now(),LocalDate.now(),SubmissionState.DONE.toString(),TEST_MOCK_STRING,TEST_MOCK_STRING,page,sort,dataFetchingEnvironment);
        Assert.assertEquals(false,CollectionUtils.isEmpty(submissionDetails));
    }

    @Test
    public void testSubmissionsSuccessList()
    {
        Query querySpy = Mockito.spy(query);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        BasicResponse<List<Submission>> response = getListBasicResponseSubmission();
        Mockito.when(submissionManagerConnector.getAllSubmissionsByStateAndDateRange(Mockito.any(Instant.class), Mockito.any(Instant.class), Mockito.any(SubmissionState.class), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt(), Mockito.any(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(response);
        Map<String, User> userMap=new HashMap<>() ;
        userMap.put(TEST_MOCK_STRING,new User());
        UserInfo userInfo= new UserInfo();
        userInfo.setUserId(TEST_MOCK_STRING);
        Mockito.doReturn(userMap).when(querySpy).getAllUsersMap(Mockito.anyString(),Mockito.anyString());
        Page page = new Page();
        page.setPageIndex(0);
        page.setPageSize(10);
        Sort sort = new Sort();
        sort.setSortBy("state");
        sort.setSortOrder(SortOrder.ASC);
        List<SubmissionDetail>submissionDetails= querySpy.submissions(Boolean.FALSE,LocalDate.now(),LocalDate.now(),SubmissionState.DONE.toString(),TEST_MOCK_STRING,TEST_MOCK_STRING,page,sort,dataFetchingEnvironment);
        Assert.assertEquals(false,CollectionUtils.isEmpty(submissionDetails));
    }

//    @Test
    public void testSubmissionsSearchSuccess()
    {
        Query querySpy = Mockito.spy(query);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Map<String, User> userMap=new HashMap<>() ;
        userMap.put(TEST_MOCK_STRING,new User());
        UserInfo userInfo= new UserInfo();
        userInfo.setUserId(TEST_MOCK_STRING);
        Mockito.doReturn(userMap).when(querySpy).getAllUsersMap(Mockito.anyString(),Mockito.anyString());
        SubmissionSearchRequest submissionSearchRequest = new SubmissionSearchRequest();
        BasicResponse<List<Submission>> submissionsResponse = getListBasicResponseSubmission();
        Mockito.when(submissionManagerConnector.searchSubmissions(Mockito.any(com.concirrus.submission.connector.submissionmanager.model.SubmissionSearchRequest.class), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt(), Mockito.anyString(), Mockito.anyString())).thenReturn(submissionsResponse);
        Page page = new Page();
        page.setPageIndex(0);
        page.setPageSize(10);
        Sort sort = new Sort();
        sort.setSortBy("state");
        sort.setSortOrder(SortOrder.ASC);
        List<SubmissionDetail> submissionDetails= querySpy.submissionsSearch(submissionSearchRequest,TEST_MOCK_STRING,TEST_MOCK_STRING,page,sort,dataFetchingEnvironment);
        Assert.assertEquals(false,CollectionUtils.isEmpty(submissionDetails));
    }

    @Test(expected = GraphQLException.class)
    public void testSubmissionsSearchGraphQlException()
    {
        Query querySpy = Mockito.spy(query);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Map<String, User> userMap=new HashMap<>() ;
        userMap.put(TEST_MOCK_STRING,new User());
        UserInfo userInfo= new UserInfo();
        userInfo.setUserId(TEST_MOCK_STRING);
        userInfo.setFirstName(TEST_MOCK_STRING);
        userInfo.setLastName(TEST_MOCK_STRING);
        Mockito.doReturn(userMap).when(querySpy).getAllUsersMap(Mockito.anyString(),Mockito.anyString());
        SubmissionSearchRequest submissionSearchRequest = new SubmissionSearchRequest();
        BasicResponse<List<Submission>> submissionsResponse = new BasicResponse().setStatus(404).setError("Error");
        Mockito.when(submissionManagerConnector.searchSubmissions(Mockito.any(com.concirrus.submission.connector.submissionmanager.model.SubmissionSearchRequest.class), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt(), Mockito.anyString(), Mockito.anyString())).thenReturn(submissionsResponse);
        Page page = new Page();
        page.setPageIndex(0);
        page.setPageSize(10);
        Sort sort = new Sort();
        sort.setSortBy("state");
        sort.setSortOrder(SortOrder.ASC);
        List<SubmissionDetail> submissionDetails= querySpy.submissionsSearch(submissionSearchRequest,TEST_MOCK_STRING,TEST_MOCK_STRING,page,sort,dataFetchingEnvironment);
    }

//    @Test
    public void testSubmissionsSearchSuccessEmptyList()
    {
        Query querySpy = Mockito.spy(query);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Map<String, User> userMap=new HashMap<>() ;
        userMap.put(TEST_MOCK_STRING,new User());
        User userInfo= new User();
        userInfo.setUserId(TEST_MOCK_STRING);
        userInfo.setFirstName(TEST_MOCK_STRING);
        userInfo.setLastName(TEST_MOCK_STRING);
        Mockito.doReturn(userMap).when(querySpy).getAllUsersMap(Mockito.anyString(),Mockito.anyString());
        SubmissionSearchRequest submissionSearchRequest = new SubmissionSearchRequest();
        BasicResponse<List<Submission>> response = getListBasicResponseSubmission();
        Mockito.when(submissionManagerConnector.searchSubmissions(Mockito.any(com.concirrus.submission.connector.submissionmanager.model.SubmissionSearchRequest.class), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt(), Mockito.anyString(), Mockito.anyString())).thenReturn(response);
        Page page = new Page();
        page.setPageIndex(0);
        page.setPageSize(10);
        Sort sort = new Sort();
        sort.setSortBy("state");
        sort.setSortOrder(SortOrder.ASC);
        List<SubmissionDetail> submissionDetails= querySpy.submissionsSearch(submissionSearchRequest,TEST_MOCK_STRING,TEST_MOCK_STRING,page,sort,dataFetchingEnvironment);
        Assert.assertEquals(false,CollectionUtils.isEmpty(submissionDetails));
    }


//    @Test
    public void testSubmissionsSearchSuccessSearchTextNotNull()
    {
        Query querySpy = Mockito.spy(query);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.createUsername(Mockito.anyString(), Mockito.anyString())).thenCallRealMethod();
        Map<String, User> userMap=new HashMap<>() ;
        User userInfo= new User();
        userInfo.setUserId(TEST_MOCK_STRING);
        userInfo.setFirstName(TEST_MOCK_STRING);
        userInfo.setLastName(TEST_MOCK_STRING);
        userMap.put(TEST_MOCK_STRING,userInfo);
        Mockito.doReturn(userMap).when(querySpy).getAllUsersMap(Mockito.anyString(),Mockito.anyString());
        SubmissionSearchRequest submissionSearchRequest = new SubmissionSearchRequest();
        submissionSearchRequest.setSearchText(TEST_MOCK_STRING);
        BasicResponse<List<Submission>> submissionsResponse = getListBasicResponseSubmission();
        Mockito.when(submissionManagerConnector.searchSubmissions(Mockito.any(com.concirrus.submission.connector.submissionmanager.model.SubmissionSearchRequest.class), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt(), Mockito.anyString(), Mockito.anyString())).thenReturn(submissionsResponse);
        Page page = new Page();
        page.setPageIndex(0);
        page.setPageSize(10);
        Sort sort = new Sort();
        sort.setSortBy("state");
        sort.setSortOrder(SortOrder.ASC);
        List<SubmissionDetail> submissionDetails= querySpy.submissionsSearch(submissionSearchRequest,TEST_MOCK_STRING,TEST_MOCK_STRING,page,sort,dataFetchingEnvironment);
        Assert.assertEquals(false,CollectionUtils.isEmpty(submissionDetails));
    }

    @Test
    public void testSubmissionsSearchSuccessSearchTextNotNullListEmpty()
    {
        Query querySpy = Mockito.spy(query);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.createUsername(Mockito.anyString(), Mockito.anyString())).thenCallRealMethod();
        Map<String, User> userMap=new HashMap<>() ;
        User userInfo= new User();
        userInfo.setUserId(TEST_MOCK_STRING);
        userInfo.setFirstName(TEST_MOCK_STRING);
        userInfo.setLastName(TEST_MOCK_STRING);
        userMap.put(TEST_MOCK_STRING,userInfo);
        Mockito.doReturn(userMap).when(querySpy).getAllUsersMap(Mockito.anyString(),Mockito.anyString());
        SubmissionSearchRequest submissionSearchRequest = new SubmissionSearchRequest();
        submissionSearchRequest.setSearchText(TEST_MOCK_STRING);
        BasicResponse<List<Submission>> response = new BasicResponse().setStatus(200).setResult(new ArrayList<>());
        Mockito.when(submissionManagerConnector.searchSubmissions(Mockito.any(com.concirrus.submission.connector.submissionmanager.model.SubmissionSearchRequest.class), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt(), Mockito.anyString(), Mockito.anyString())).thenReturn(response);
        Page page = new Page();
        page.setPageIndex(0);
        page.setPageSize(10);
        Sort sort = new Sort();
        sort.setSortBy("state");
        sort.setSortOrder(SortOrder.ASC);
        List<SubmissionDetail> submissionDetails= querySpy.submissionsSearch(submissionSearchRequest,TEST_MOCK_STRING,TEST_MOCK_STRING,page,sort,dataFetchingEnvironment);
        Assert.assertEquals(true,CollectionUtils.isEmpty(submissionDetails));
    }


    @Test
    public void testReferenceData() {
        query.referenceData();
    }

    @Test
    public void testLicence() {
        query.licence();
    }

    @NotNull
    private SubmissionDetailResponse getSubmissionDetailsResponse(String id) {
        SubmissionDetailResponse submission = new SubmissionDetailResponse();
        submission.setId(UUID.randomUUID().toString());
        submission.setAssigneeId(TEST_MOCK_STRING);
        submission.setState(SubmissionState.IN_REVIEW);
        VesselInfo vesselInfo = new VesselInfo();
        vesselInfo.setImo("8318998");
        vesselInfo.setPremium(7236.32);
        vesselInfo.setCoverFromDate(ZonedDateTime.now().toString());
        vesselInfo.setCoverToDate(ZonedDateTime.now().toString());
        vesselInfo.setProductType(TEST_MOCK_STRING);
        submission.setVessels(Arrays.asList(vesselInfo));
        submission.setProductName("Hull");
        submission.setProductLine("HullAndMachinery");
        HashMap<String,Object> policyInfo = new HashMap<>();
        policyInfo.put("counterPartyId", id);
        HashMap<String,Object> additionalInfo= new HashMap<>();
        additionalInfo.put("policyInfo",policyInfo);
        submission.setAdditionalInfo(additionalInfo);
        return submission;
    }

    @Test
    public void me(){
        UserInfoResponse userInfoResponse = new UserInfoResponse();
        userInfoResponse.setId("Test");
        userInfoResponse.setEmail("<EMAIL>");
        userInfoResponse.setFirstName("Test");
        userInfoResponse.setLastName("Test");
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(accessManagementSal.getUserInfo(Mockito.any(),Mockito.any())).thenReturn(userInfoResponse);
        Assert.assertEquals(true,!Objects.isNull(userInfoResponse));
    }

    private BasicResponse<List<Submission>> getListBasicResponseSubmission() {
        Submission submission = new Submission();
        submission.setId(UUID.randomUUID().toString());
        submission.setAssigneeId(TEST_MOCK_STRING);
        VesselInfo vesselInfo = new VesselInfo();
        vesselInfo.setImo("8318998");
        vesselInfo.setPremium(7236.32);
        vesselInfo.setCoverFromDate(ZonedDateTime.now().toString());
        vesselInfo.setCoverToDate(ZonedDateTime.now().toString());
        vesselInfo.setProductType(TEST_MOCK_STRING);
        submission.setVessels(Arrays.asList(vesselInfo));
        com.concirrus.submission.connector.submissionmanager.model.RiskInsight riskInsight = new com.concirrus.submission.connector.submissionmanager.model.RiskInsight();
        riskInsight.setExpectedLoss(32.23);
        riskInsight.setScore(3);
        riskInsight.setSeverity(4);
        riskInsight.setFrequency(3);
        submission.setRiskInsight(riskInsight);
        BasicResponse<List<Submission>> response = new BasicResponse().setStatus(200).setResult(Arrays.asList(submission));
        return response;
    }

    @Test
    public void testPoliciesForAccountBySubmissionIdSucccess()
    {
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        SubmissionDetailResponse submission = getSubmissionDetailsResponse(UUID.randomUUID().toString());
        BasicResponse<List<SubmissionDetailResponse>> response = new BasicResponse().setStatus(200).setResult(Arrays.asList(submission));
        Mockito.when(submissionManagerConnector.getSubmissionsByIds(Mockito.anyList(),Mockito.anyString(),Mockito.anyBoolean())).thenReturn(response);
        PolicySummary policySummary = new PolicySummary();
        policySummary.setAccountId(TEST_MOCK_STRING);
        policySummary.setInsurancePolicyId(TEST_MOCK_STRING);
        policySummary.setPolicyNumber(TEST_MOCK_STRING);
        policySummary.setPolicyStatus(PolicyStatus.ACTIVE);
        policySummary.setPolicyType(PolicyType.New);
        BasicResponse<List<PolicySummary>> policySummaryResponse = new BasicResponse<List<PolicySummary>>().setResult(Arrays.asList(policySummary)).setStatus(200);
        Product product = new Product();
        product.setName("HullandWar");
        BasicResponse<List<Product>> productResponse = new BasicResponse<List<Product>>().setResult(Arrays.asList(product)).setStatus(200);
        Mockito.when(questInsightsConnector.getProducts(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(productResponse);
        Mockito.when(policyIntegrationConnector.getPoliciesByAccountIdAndProductAndProductLine(Mockito.any(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(policySummaryResponse);
        List<com.concirrus.submissions.graphql.model.PolicySummary> policySummaries=query.policiesForAccountBySubmissionId(TEST_MOCK_STRING,dataFetchingEnvironment);
    }

    @Test(expected = GraphQLException.class)
    public void testPoliciesForAccountBySubmissionIdFail()
    {
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        SubmissionDetailResponse submission = getSubmissionDetailsResponse(UUID.randomUUID().toString());
        BasicResponse<List<SubmissionDetailResponse>> response = new BasicResponse().setStatus(200).setResult(Arrays.asList(submission));
        Mockito.when(submissionManagerConnector.getSubmissionsByIds(Mockito.anyList(),Mockito.anyString(),Mockito.anyBoolean())).thenReturn(response);
        BasicResponse<List<PolicySummary>> policySummaryResponse = new BasicResponse<List<PolicySummary>>().setError(TEST_MOCK_STRING).setStatus(500);
        Product product = new Product();
        product.setName("HullandWar");
        BasicResponse<List<Product>> productResponse = new BasicResponse<List<Product>>().setResult(Arrays.asList(product)).setStatus(200);
        Mockito.when(questInsightsConnector.getProducts(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(productResponse);

        Mockito.when(policyIntegrationConnector.getPoliciesByAccountIdAndProductAndProductLine(Mockito.any(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(policySummaryResponse);
        List<com.concirrus.submissions.graphql.model.PolicySummary> policySummaries=query.policiesForAccountBySubmissionId(TEST_MOCK_STRING,dataFetchingEnvironment);
    }

    @Test(expected = GraphQLException.class)
    public void testPoliciesForAccountBySubmissionId_SubmissionNotFound()
    {
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        SubmissionDetailResponse submission = getSubmissionDetailsResponse(UUID.randomUUID().toString());
        BasicResponse<List<SubmissionDetailResponse>> response = new BasicResponse().setStatus(404).setResult(null);
        Mockito.when(submissionManagerConnector.getSubmissionsByIds(Mockito.anyList(),Mockito.anyString(),Mockito.anyBoolean())).thenReturn(response);
        List<com.concirrus.submissions.graphql.model.PolicySummary> policySummaries=query.policiesForAccountBySubmissionId(TEST_MOCK_STRING,dataFetchingEnvironment);
    }
}
