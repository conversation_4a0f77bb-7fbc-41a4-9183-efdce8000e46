package com.concirrus.submissions.resolvers;

import com.concirrus.submission.connector.accessmanagement.AccessManagementConnector;
import com.concirrus.submission.connector.workflowmanager.WorkflowManagerConnector;
import com.concirrus.submissions.common.CommonUtil;
import com.concirrus.submissions.connector.questinsights.QuestInsightsConnector;
import com.concirrus.submissions.graphql.resolvers.Mutation;
import com.concirrus.submissions.integration.AccessManagementSal;
import graphql.schema.DataFetchingEnvironment;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
public class MutationTest {
    @Mock
    DataFetchingEnvironment dataFetchingEnvironment;

    @Mock
    Mutation mutation;

    @Mock
    AccessManagementConnector accessManagementConnector;

    @Mock
    AccessManagementSal accessManagementSal;

    @Mock
    CommonUtil commonUtil;

    @Mock
    QuestInsightsConnector questInsightsConnector;

    @Mock
    WorkflowManagerConnector workflowManagerConnector;

    @Before
    public void setUp() {
        mutation = new Mutation(accessManagementConnector, accessManagementSal, commonUtil, questInsightsConnector, workflowManagerConnector);
    }

    @Test
    public void testSubmission()
    {
        mutation.submission(dataFetchingEnvironment);
    }

}
