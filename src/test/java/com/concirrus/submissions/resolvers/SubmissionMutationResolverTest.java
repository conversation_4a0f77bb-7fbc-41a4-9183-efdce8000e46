package com.concirrus.submissions.resolvers;

import com.concirrus.quest.common.rest.exception.BadRequestException;
import com.concirrus.quest.common.rest.model.BasicResponse;
import com.concirrus.submission.connector.accessmanagement.model.UserInfoResponse;
import com.concirrus.submission.connector.policyintegration.PolicyIntegrationConnector;
import com.concirrus.submission.connector.policyintegration.model.PolicySummary;
import com.concirrus.submission.connector.submissionmanager.SubmissionManagerConnector;
import com.concirrus.submission.connector.submissionmanager.model.Submission;
import com.concirrus.submission.connector.submissionmanager.model.SubmissionDetailResponse;
import com.concirrus.submission.connector.submissionmanager.model.SubmissionState;
import com.concirrus.submission.connector.submissionmanager.model.VesselInfo;
import com.concirrus.submissions.common.CommonUtil;
import com.concirrus.submissions.connector.questinsights.QuestInsightsConnector;
import com.concirrus.submissions.connector.questinsights.model.referencedata.Product;
import com.concirrus.submissions.connector.questinsights.model.user.UserInfo;
import com.concirrus.submissions.graphql.model.DeleteSubmissionRequest;
import com.concirrus.submissions.graphql.model.SubmissionEditRequest;
import com.concirrus.submissions.graphql.model.SubmissionMutation;
import com.concirrus.submissions.graphql.resolvers.SubmissionMutationResolver;
import com.concirrus.submissions.graphql.security.Sentry;
import com.concirrus.submissions.graphql.security.User;
import com.concirrus.submissions.integration.AccessManagementSal;
import com.concirrus.submissions.model.SubmissionStateStatusChangeRequest;
import com.concirrus.submissions.service.mapper.SubmissionMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import graphql.GraphQLException;
import graphql.schema.DataFetchingEnvironment;
import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.time.ZonedDateTime;
import java.util.*;
import java.util.logging.Logger;

@RunWith(SpringJUnit4ClassRunner.class)
public class SubmissionMutationResolverTest {

    @Mock
    SubmissionMutationResolver submissionMutationResolver;

    @Mock
    ObjectMapper objectMapper;

    @Mock
    SubmissionMutation submissionMutation;

    @Mock
    QuestInsightsConnector questInsightsConnector;

    @Mock
    SubmissionManagerConnector submissionManagerConnector;

    @Mock
    SubmissionMapper submissionMapper;

    @Mock
    DataFetchingEnvironment dataFetchingEnvironment;

    @Mock
    Logger LOG;

    @Mock
    CommonUtil commonUtil;

    @Mock
    Sentry sentry;

    @Mock
    PolicyIntegrationConnector policyIntegrationConnector;

    @Mock
    AccessManagementSal accessManagementSal;

    private static String TEST_MOCK_STRING = "test";

    @Before
    public void setUp() {
        objectMapper = new ObjectMapper();
        submissionMutationResolver = new SubmissionMutationResolver(questInsightsConnector,submissionManagerConnector,objectMapper,commonUtil,submissionMapper,policyIntegrationConnector,sentry,accessManagementSal);
    }

    @Test
    public void testCreateSuccess() {
        DataFetchingEnvironment dataFetchingEnvironment = Mockito.mock(DataFetchingEnvironment.class);
        Mockito.when(commonUtil.getClientId(dataFetchingEnvironment)).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.isFeatureEnabled(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
        BasicResponse<String> response = new BasicResponse().setStatus(HttpStatus.CREATED.value()).setResult(UUID.randomUUID().toString());
        Mockito.when(submissionManagerConnector.createSubmission(Mockito.any(), Mockito.any())).thenReturn(response);
        String submissionId = submissionMutationResolver.create(submissionMutation,new com.concirrus.submissions.model.SubmissionCreationRequest(), dataFetchingEnvironment);
        Assert.assertNotNull(submissionId);
    }

    @Test(expected = RuntimeException.class)
    public void testCreateError() {
        DataFetchingEnvironment dataFetchingEnvironment = Mockito.mock(DataFetchingEnvironment.class);
        BasicResponse<String> response = new BasicResponse().setStatus(HttpStatus.BAD_REQUEST.value()).setError(new Error("Bad Request"));
        Mockito.when(submissionManagerConnector.createSubmission(Mockito.any(), Mockito.any())).thenReturn(response);
        String submissionId = submissionMutationResolver.create(submissionMutation,new com.concirrus.submissions.model.SubmissionCreationRequest(), dataFetchingEnvironment);
    }

    @Test
    public void testEditSubmissionSuccess()
    {
        DataFetchingEnvironment dataFetchingEnvironment =Mockito.mock(DataFetchingEnvironment.class) ;
        Mockito.when(commonUtil.getClientId(dataFetchingEnvironment)).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.isFeatureEnabled(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
        SubmissionMapper mapper = Mockito.mock(SubmissionMapper.class);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Submission submission =new Submission();
        submission.setState(SubmissionState.INBOX);
        BasicResponse<Submission> response = new BasicResponse().setStatus(200).setResult(submission);
        Mockito.when(submissionManagerConnector.getSubmissionById(Mockito.anyString(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(response);
        Mockito.when(submissionMapper.getInstance(Mockito.any(SubmissionEditRequest.class),Mockito.anyString())).thenReturn(new com.concirrus.submission.connector.submissionmanager.model.SubmissionRequest());
        BasicResponse<Submission> submissionBasicResponse = new BasicResponse().setStatus(200).setResult(new Submission());
        Mockito.when(submissionManagerConnector.editSubmission(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(submissionBasicResponse);
        boolean editSubmissionResponse= submissionMutationResolver.editSubmission(submissionMutation,UUID.randomUUID().toString(),new SubmissionEditRequest(),dataFetchingEnvironment);
        Assert.assertEquals(true,editSubmissionResponse);
    }

    @Test(expected = GraphQLException.class)
    public void testEditSubmissionError_DoneState()
    {
        DataFetchingEnvironment dataFetchingEnvironment =Mockito.mock(DataFetchingEnvironment.class) ;
        SubmissionMapper mapper = Mockito.mock(SubmissionMapper.class);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Submission submission =new Submission();
        submission.setState(SubmissionState.DONE);
        BasicResponse<Submission> response = new BasicResponse().setStatus(200).setResult(submission);
        Mockito.when(submissionManagerConnector.getSubmissionById(Mockito.anyString(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(response);
        Mockito.when(submissionMapper.getInstance(Mockito.any(SubmissionEditRequest.class),Mockito.anyString())).thenReturn(new com.concirrus.submission.connector.submissionmanager.model.SubmissionRequest());
        BasicResponse<Submission> submissionBasicResponse = new BasicResponse().setStatus(200).setResult(new Submission());
        Mockito.when(submissionManagerConnector.editSubmission(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(submissionBasicResponse);
        boolean editSubmissionResponse= submissionMutationResolver.editSubmission(submissionMutation,UUID.randomUUID().toString(),new SubmissionEditRequest(),dataFetchingEnvironment);
    }

    @Test(expected = GraphQLException.class)
    public void testEditSubmissionError_InReviewState()
    {
        DataFetchingEnvironment dataFetchingEnvironment =Mockito.mock(DataFetchingEnvironment.class) ;
        SubmissionMapper mapper = Mockito.mock(SubmissionMapper.class);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Submission submission =new Submission();
        submission.setState(SubmissionState.IN_REVIEW);
        BasicResponse<Submission> response = new BasicResponse().setStatus(200).setResult(submission);
        Mockito.when(submissionManagerConnector.getSubmissionById(Mockito.anyString(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(response);
        Mockito.when(submissionMapper.getInstance(Mockito.any(SubmissionEditRequest.class),Mockito.anyString())).thenReturn(new com.concirrus.submission.connector.submissionmanager.model.SubmissionRequest());
        BasicResponse<Submission> submissionBasicResponse = new BasicResponse().setStatus(200).setResult(new Submission());
        Mockito.when(submissionManagerConnector.editSubmission(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(submissionBasicResponse);
        boolean editSubmissionResponse= submissionMutationResolver.editSubmission(submissionMutation,UUID.randomUUID().toString(),new SubmissionEditRequest(),dataFetchingEnvironment);
    }

    @Test(expected = GraphQLException.class)
    public void testEditSubmissionError_Quoted()
    {
        DataFetchingEnvironment dataFetchingEnvironment =Mockito.mock(DataFetchingEnvironment.class) ;
        SubmissionMapper mapper = Mockito.mock(SubmissionMapper.class);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Submission submission =new Submission();
        submission.setState(SubmissionState.QUOTED);
        BasicResponse<Submission> response = new BasicResponse().setStatus(200).setResult(submission);
        Mockito.when(submissionManagerConnector.getSubmissionById(Mockito.anyString(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(response);
        Mockito.when(submissionMapper.getInstance(Mockito.any(SubmissionEditRequest.class),Mockito.anyString())).thenReturn(new com.concirrus.submission.connector.submissionmanager.model.SubmissionRequest());
        BasicResponse<Submission> submissionBasicResponse = new BasicResponse().setStatus(200).setResult(new Submission());
        Mockito.when(submissionManagerConnector.editSubmission(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(submissionBasicResponse);
        boolean editSubmissionResponse= submissionMutationResolver.editSubmission(submissionMutation,UUID.randomUUID().toString(),new SubmissionEditRequest(),dataFetchingEnvironment);
    }

    @Test(expected = GraphQLException.class)
    public void testEditSubmissionError()
    {
        DataFetchingEnvironment dataFetchingEnvironment =Mockito.mock(DataFetchingEnvironment.class) ;
        Mockito.when(commonUtil.getClientId(dataFetchingEnvironment)).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.isFeatureEnabled(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
        Submission submission =new Submission();
        submission.setState(SubmissionState.INBOX);
        BasicResponse<Submission> response = new BasicResponse().setStatus(200).setResult(submission);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(submissionManagerConnector.getSubmissionById(Mockito.anyString(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(response);
        Mockito.when(submissionMapper.getInstance(Mockito.any(SubmissionEditRequest.class),Mockito.anyString())).thenReturn(new com.concirrus.submission.connector.submissionmanager.model.SubmissionRequest());
        BasicResponse<Submission> submissionBasicResponse = new BasicResponse().setStatus(400).setResult(new BadRequestException());
        Mockito.when(submissionManagerConnector.editSubmission(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(submissionBasicResponse);
        boolean editSubmissionResponse = submissionMutationResolver.editSubmission(submissionMutation,UUID.randomUUID().toString(),new SubmissionEditRequest(),dataFetchingEnvironment);
    }

    @Test(expected = RuntimeException.class)
    public void testEditSubmissionErrorFindSubmissionById()
    {
        DataFetchingEnvironment dataFetchingEnvironment =Mockito.mock(DataFetchingEnvironment.class) ;
        BasicResponse<Submission> response = new BasicResponse().setStatus(404).setResult(null);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(submissionManagerConnector.getSubmissionById(Mockito.anyString(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(response);
        boolean editSubmissionResponse = submissionMutationResolver.editSubmission(submissionMutation,UUID.randomUUID().toString(),new SubmissionEditRequest(),dataFetchingEnvironment);
    }

    @Test
    public void testAssignSubmissionSuccess()
    {
        DataFetchingEnvironment dataFetchingEnvironment =Mockito.mock(DataFetchingEnvironment.class) ;
        Mockito.when(commonUtil.getClientId(dataFetchingEnvironment)).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.isFeatureEnabled(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
        SubmissionMutationResolver submissionMutationResolverSpy = Mockito.spy(submissionMutationResolver);
        Mockito.when(submissionMutationResolverSpy.validateUser(Mockito.anyString(),Mockito.any())).thenReturn(true);
        BasicResponse<Boolean> response = new BasicResponse().setStatus(200).setResult(true);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(submissionManagerConnector.assignUserToSubmission(Mockito.anyString(),Mockito.any(),Mockito.any())).thenReturn(response);
        boolean assignSubmissionResponse = submissionMutationResolverSpy.assignSubmission(submissionMutation,UUID.randomUUID().toString(),UUID.randomUUID().toString(),dataFetchingEnvironment);
        Assert.assertEquals(true,assignSubmissionResponse);

    }

    @Test(expected = GraphQLException.class)
    public void testAssignSubmissionError()
    {
        DataFetchingEnvironment dataFetchingEnvironment =Mockito.mock(DataFetchingEnvironment.class) ;
        SubmissionMutationResolver submissionMutationResolverSpy = Mockito.spy(submissionMutationResolver);
        Mockito.when(submissionMutationResolverSpy.validateUser(Mockito.anyString(),Mockito.any())).thenReturn(true);
        BasicResponse<Boolean> response = new BasicResponse().setStatus(404).setResult(false);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(submissionManagerConnector.assignUserToSubmission(Mockito.anyString(),Mockito.any(),Mockito.any())).thenReturn(response);
        boolean assignSubmissionResponse = submissionMutationResolverSpy.assignSubmission(submissionMutation,UUID.randomUUID().toString(),UUID.randomUUID().toString(),dataFetchingEnvironment);
    }


    @Test
    public void testValidateUserNullUserId()
    {
        DataFetchingEnvironment dataFetchingEnvironment =Mockito.mock(DataFetchingEnvironment.class) ;
        String userId = null;
        boolean response = submissionMutationResolver.validateUser(userId,dataFetchingEnvironment);
        Assert.assertEquals(true,response);
    }

    @Test
    public void testValidateUserEmptyUserId()
    {
        DataFetchingEnvironment dataFetchingEnvironment =Mockito.mock(DataFetchingEnvironment.class) ;
        String userId = "";
        boolean response = submissionMutationResolver.validateUser(userId,dataFetchingEnvironment);
        Assert.assertEquals(true,response);
    }

    @Test
    public void testValidateUserSuccess()
    {
        DataFetchingEnvironment dataFetchingEnvironment =Mockito.mock(DataFetchingEnvironment.class) ;
        BasicResponse<UserInfoResponse> response = new BasicResponse().setStatus(200).setResult(new UserInfo());
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(accessManagementSal.getUserById(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(response);
        boolean validateUserResponse = submissionMutationResolver.validateUser(TEST_MOCK_STRING,dataFetchingEnvironment);
        Assert.assertEquals(true,validateUserResponse);
    }

    @Test(expected = GraphQLException.class)
    public void testValidateUserNotFound()
    {
        DataFetchingEnvironment dataFetchingEnvironment =Mockito.mock(DataFetchingEnvironment.class) ;
        BasicResponse<UserInfoResponse> response = new BasicResponse().setStatus(404).setResult(null);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(accessManagementSal.getUserById(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(response);
        submissionMutationResolver.validateUser(TEST_MOCK_STRING,dataFetchingEnvironment);
    }

    @Test(expected = GraphQLException.class)
    public void testValidateUserError()
    {
        DataFetchingEnvironment dataFetchingEnvironment =Mockito.mock(DataFetchingEnvironment.class) ;
        BasicResponse<UserInfoResponse> response = new BasicResponse().setStatus(400).setResult(null);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(accessManagementSal.getUserById(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(response);
        submissionMutationResolver.validateUser(TEST_MOCK_STRING,dataFetchingEnvironment);
    }

    @Test(expected = RuntimeException.class)
    public void testValidateUserErrorInsightConnector()
    {
        DataFetchingEnvironment dataFetchingEnvironment =Mockito.mock(DataFetchingEnvironment.class) ;
        BasicResponse<UserInfo> response = new BasicResponse().setStatus(400).setResult(null);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(questInsightsConnector.getUserById(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenThrow(new RuntimeException());
        submissionMutationResolver.validateUser(TEST_MOCK_STRING,dataFetchingEnvironment);
    }

    @Test
    public void testEditSubmissionStateStatusSuccess()
    {
        DataFetchingEnvironment dataFetchingEnvironment =Mockito.mock(DataFetchingEnvironment.class) ;
        Mockito.when(commonUtil.getClientId(dataFetchingEnvironment)).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.isFeatureEnabled(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
        com.concirrus.submission.connector.submissionmanager.model.SubmissionStateStatusChangeRequest mockRequest= new com.concirrus.submission.connector.submissionmanager.model.SubmissionStateStatusChangeRequest();
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        BasicResponse<Boolean> response = new BasicResponse().setStatus(200).setResult(true);
        Mockito.when(submissionManagerConnector.changeSubmissionStateStatus(Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.any())).thenReturn(response);
        boolean result =submissionMutationResolver.editSubmissionStateStatus(submissionMutation,new SubmissionStateStatusChangeRequest(),TEST_MOCK_STRING,dataFetchingEnvironment);
        Assert.assertEquals(true,result);
    }

    @Test(expected = GraphQLException.class)
    public void testEditSubmissionStateStatusFalse()
    {
        DataFetchingEnvironment dataFetchingEnvironment =Mockito.mock(DataFetchingEnvironment.class) ;
        com.concirrus.submission.connector.submissionmanager.model.SubmissionStateStatusChangeRequest mockRequest= new com.concirrus.submission.connector.submissionmanager.model.SubmissionStateStatusChangeRequest();
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        BasicResponse<Boolean> response = new BasicResponse().setStatus(400).setResult(false).setError("Error");
        Mockito.when(submissionManagerConnector.changeSubmissionStateStatus(Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.any())).thenReturn(response);
        submissionMutationResolver.editSubmissionStateStatus(submissionMutation,new SubmissionStateStatusChangeRequest(),TEST_MOCK_STRING,dataFetchingEnvironment);
    }

    @Test(expected = RuntimeException.class)
    public void testEditSubmissionStateStatusRuntimeException()
    {
        DataFetchingEnvironment dataFetchingEnvironment =Mockito.mock(DataFetchingEnvironment.class) ;
        com.concirrus.submission.connector.submissionmanager.model.SubmissionStateStatusChangeRequest mockRequest= new com.concirrus.submission.connector.submissionmanager.model.SubmissionStateStatusChangeRequest();
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        BasicResponse<Boolean> response = new BasicResponse().setStatus(400).setError("Error").setResult(false);
        Mockito.when(submissionManagerConnector.changeSubmissionStateStatus(Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.any())).thenReturn(response);
        boolean result =submissionMutationResolver.editSubmissionStateStatus(submissionMutation,new SubmissionStateStatusChangeRequest(),TEST_MOCK_STRING,dataFetchingEnvironment);
    }
    @NotNull
    private SubmissionDetailResponse getSubmissionDetailsResponse(String id) {
        SubmissionDetailResponse submission = new SubmissionDetailResponse();
        submission.setId(UUID.randomUUID().toString());
        submission.setAssigneeId(TEST_MOCK_STRING);
        submission.setState(SubmissionState.IN_REVIEW);
        VesselInfo vesselInfo = new VesselInfo();
        vesselInfo.setImo("8318998");
        vesselInfo.setPremium(7236.32);
        vesselInfo.setCoverFromDate(ZonedDateTime.now().toString());
        vesselInfo.setCoverToDate(ZonedDateTime.now().toString());
        vesselInfo.setProductType(TEST_MOCK_STRING);
        submission.setVessels(Arrays.asList(vesselInfo));
        submission.setProductName("Hull");
        submission.setProductLine("HullAndMachinery");
        HashMap<String,Object> policyInfo = new HashMap<>();
        policyInfo.put("counterPartyId", id);
        HashMap<String,Object> additionalInfo= new HashMap<>();
        additionalInfo.put("policyInfo",policyInfo);
        submission.setAdditionalInfo(additionalInfo);
        return submission;
    }
    @Test
    public void testDeleteSubmissionSuccess() throws JSONException {
        DeleteSubmissionRequest deleteSubmissionRequest = new DeleteSubmissionRequest();
        String[] role = {};
        User user = new User("id", "questToken", new HashSet(new HashSet<>(Arrays.asList(role))) , true);
        Mockito.when(sentry.getLoggedInUser(dataFetchingEnvironment)).thenReturn(user);
        UserInfoResponse userInfoResponse = new UserInfoResponse();
        userInfoResponse.setId("id");
        Mockito.when(accessManagementSal.getUserInfo(Mockito.anyString(),Mockito.anyString())).thenReturn(userInfoResponse);
        String id = UUID.randomUUID().toString();
        deleteSubmissionRequest.setDeleteAssociatedAccount(true);
        deleteSubmissionRequest.setDeletionReason("test");
        deleteSubmissionRequest.setSubmissionId(id);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.isFeatureEnabled(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        SubmissionDetailResponse submission = getSubmissionDetailsResponse(id);
        BasicResponse<List<SubmissionDetailResponse>> response = new BasicResponse().setStatus(200).setResult(Arrays.asList(submission));
        Mockito.when(submissionManagerConnector.getSubmissionsByIds(Mockito.anyList(),Mockito.anyString(),Mockito.anyBoolean())).thenReturn(response);
        BasicResponse<UserInfo> userInfoBasicResponse = new BasicResponse<>();
        userInfoBasicResponse.setStatus(200);
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(id);
        userInfo.setFirstName("Rachita");
        userInfoBasicResponse.setResult(userInfo);
        Mockito.when(questInsightsConnector.getUserById(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(userInfoBasicResponse);
        BasicResponse<Boolean> deletePolicyResponse =  new BasicResponse<Boolean>().setStatus(200).setResult(true);
        Mockito.when(policyIntegrationConnector.deletePolicy(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(deletePolicyResponse);
        BasicResponse<List<PolicySummary>> policySummaryResponse = new BasicResponse<List<PolicySummary>>().setResult(Collections.EMPTY_LIST).setStatus(200);
        Product product = new Product();
        product.setName("HullandWar");
        BasicResponse<List<Product>> productResponse = new BasicResponse<List<Product>>().setResult(Arrays.asList(product)).setStatus(200);
        Mockito.when(questInsightsConnector.getProducts(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(productResponse);
        Mockito.when(policyIntegrationConnector.getPoliciesByAccountIdAndProductAndProductLine(Mockito.any(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(policySummaryResponse);
        BasicResponse<Boolean> deleteAccountResponse = new BasicResponse<Boolean>().setStatus(200).setResult(Boolean.TRUE);
        Mockito.when(questInsightsConnector.deleteAccountByAccountId(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(deleteAccountResponse);
        BasicResponse<Boolean> deleteSubmissionResponse = new BasicResponse<Boolean>().setStatus(200).setResult(Boolean.TRUE);
        Mockito.when(submissionManagerConnector.deleteSubmission(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(deleteSubmissionResponse);
        Boolean result=submissionMutationResolver.deleteSubmission(submissionMutation, deleteSubmissionRequest,dataFetchingEnvironment);
        Assert.assertTrue(result);
    }

    @Test(expected = GraphQLException.class)
    public void testDeleteSubmission_submissionNotFound() throws JSONException {
        String[] role = {};
        User user = new User("id", "questToken", new HashSet(new HashSet<>(Arrays.asList(role))) , true);
        Mockito.when(sentry.getLoggedInUser(dataFetchingEnvironment)).thenReturn(user);
        UserInfoResponse userInfoResponse = new UserInfoResponse();
        userInfoResponse.setId("id");
        Mockito.when(accessManagementSal.getUserInfo(Mockito.anyString(),Mockito.anyString())).thenReturn(userInfoResponse);
        DeleteSubmissionRequest deleteSubmissionRequest = new DeleteSubmissionRequest();
        String id = UUID.randomUUID().toString();
        deleteSubmissionRequest.setDeleteAssociatedAccount(true);
        deleteSubmissionRequest.setDeletionReason("test");
        deleteSubmissionRequest.setSubmissionId(id);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        SubmissionDetailResponse submission = getSubmissionDetailsResponse(id);
        BasicResponse<List<SubmissionDetailResponse>> response = new BasicResponse().setStatus(404).setError("NotFound");
        Mockito.when(submissionManagerConnector.getSubmissionsByIds(Mockito.anyList(),Mockito.anyString(),Mockito.anyBoolean())).thenReturn(response);
        Boolean result=submissionMutationResolver.deleteSubmission(submissionMutation, deleteSubmissionRequest,dataFetchingEnvironment);
    }

    @Test(expected = GraphQLException.class)
    public void testDeleteSubmission_submissionListEmpty() throws JSONException {
        String[] role = {};
        User user = new User("id", "questToken", new HashSet(new HashSet<>(Arrays.asList(role))) , true);
        Mockito.when(sentry.getLoggedInUser(dataFetchingEnvironment)).thenReturn(user);
        UserInfoResponse userInfoResponse = new UserInfoResponse();
        userInfoResponse.setId("id");
        Mockito.when(accessManagementSal.getUserInfo(Mockito.anyString(),Mockito.anyString())).thenReturn(userInfoResponse);
        DeleteSubmissionRequest deleteSubmissionRequest = new DeleteSubmissionRequest();
        String id = UUID.randomUUID().toString();
        deleteSubmissionRequest.setDeleteAssociatedAccount(true);
        deleteSubmissionRequest.setDeletionReason("test");
        deleteSubmissionRequest.setSubmissionId(id);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        SubmissionDetailResponse submission = getSubmissionDetailsResponse(id);
        BasicResponse<List<SubmissionDetailResponse>> response = new BasicResponse().setStatus(200).setResult(null);
        Mockito.when(submissionManagerConnector.getSubmissionsByIds(Mockito.anyList(),Mockito.anyString(),Mockito.anyBoolean())).thenReturn(response);
        Boolean result=submissionMutationResolver.deleteSubmission(submissionMutation, deleteSubmissionRequest,dataFetchingEnvironment);
    }

    @Test(expected = GraphQLException.class)
    public void testDeleteSubmission_ValidateUser_404() throws JSONException {
        String[] role = {};
        User user = new User("id", "questToken", new HashSet(new HashSet<>(Arrays.asList(role))) , true);
        Mockito.when(sentry.getLoggedInUser(dataFetchingEnvironment)).thenReturn(user);
        UserInfoResponse userInfoResponse = new UserInfoResponse();
        userInfoResponse.setId("id");
        Mockito.when(accessManagementSal.getUserInfo(Mockito.anyString(),Mockito.anyString())).thenReturn(userInfoResponse);

        DeleteSubmissionRequest deleteSubmissionRequest = new DeleteSubmissionRequest();
        String id = UUID.randomUUID().toString();
        deleteSubmissionRequest.setDeleteAssociatedAccount(true);
        deleteSubmissionRequest.setDeletionReason("test");
        deleteSubmissionRequest.setSubmissionId(id);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        SubmissionDetailResponse submission = getSubmissionDetailsResponse(id);
        BasicResponse<List<SubmissionDetailResponse>> response = new BasicResponse().setStatus(200).setResult(Arrays.asList(submission));
        Mockito.when(submissionManagerConnector.getSubmissionsByIds(Mockito.anyList(),Mockito.anyString(),Mockito.anyBoolean())).thenReturn(response);
        BasicResponse<UserInfo> userInfoBasicResponse = new BasicResponse<>();
        userInfoBasicResponse.setStatus(404);
        userInfoBasicResponse.setError("User not found");
        Mockito.when(questInsightsConnector.getUserById(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(userInfoBasicResponse);
        Boolean result=submissionMutationResolver.deleteSubmission(submissionMutation, deleteSubmissionRequest,dataFetchingEnvironment);
    }

    @Test(expected = GraphQLException.class)
    public void testDeleteSubmission_ValidateUser_500() throws JSONException {
        String[] role = {};
        User user = new User("id", "questToken", new HashSet(new HashSet<>(Arrays.asList(role))) , true);
        Mockito.when(sentry.getLoggedInUser(dataFetchingEnvironment)).thenReturn(user);
        UserInfoResponse userInfoResponse = new UserInfoResponse();
        userInfoResponse.setId("id");
        Mockito.when(accessManagementSal.getUserInfo(Mockito.anyString(),Mockito.anyString())).thenReturn(userInfoResponse);
        DeleteSubmissionRequest deleteSubmissionRequest = new DeleteSubmissionRequest();
        String id = UUID.randomUUID().toString();
        deleteSubmissionRequest.setDeleteAssociatedAccount(true);
        deleteSubmissionRequest.setDeletionReason("test");
        deleteSubmissionRequest.setSubmissionId(id);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        SubmissionDetailResponse submission = getSubmissionDetailsResponse(id);
        BasicResponse<List<SubmissionDetailResponse>> response = new BasicResponse().setStatus(200).setResult(Arrays.asList(submission));
        Mockito.when(submissionManagerConnector.getSubmissionsByIds(Mockito.anyList(),Mockito.anyString(),Mockito.anyBoolean())).thenReturn(response);
        BasicResponse<UserInfo> userInfoBasicResponse = new BasicResponse<>();
        userInfoBasicResponse.setStatus(500);
        userInfoBasicResponse.setError("User not found");
        Mockito.when(questInsightsConnector.getUserById(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(userInfoBasicResponse);
        Boolean result=submissionMutationResolver.deleteSubmission(submissionMutation, deleteSubmissionRequest,dataFetchingEnvironment);
    }


    @Test(expected = GraphQLException.class)
    public void testDeleteSubmission_DeleteAccountFail() throws JSONException {
        String[] role = {};
        User user = new User("id", "questToken", new HashSet(new HashSet<>(Arrays.asList(role))) , true);
        Mockito.when(sentry.getLoggedInUser(dataFetchingEnvironment)).thenReturn(user);
        UserInfoResponse userInfoResponse = new UserInfoResponse();
        userInfoResponse.setId("id");
        Mockito.when(accessManagementSal.getUserInfo(Mockito.anyString(),Mockito.anyString())).thenReturn(userInfoResponse);
        DeleteSubmissionRequest deleteSubmissionRequest = new DeleteSubmissionRequest();
        String id = UUID.randomUUID().toString();
        deleteSubmissionRequest.setDeleteAssociatedAccount(true);
        deleteSubmissionRequest.setDeletionReason("test");
        deleteSubmissionRequest.setSubmissionId(id);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        SubmissionDetailResponse submission = getSubmissionDetailsResponse(id);
        BasicResponse<List<SubmissionDetailResponse>> response = new BasicResponse().setStatus(200).setResult(Arrays.asList(submission));
        Mockito.when(submissionManagerConnector.getSubmissionsByIds(Mockito.anyList(),Mockito.anyString(),Mockito.anyBoolean())).thenReturn(response);
        BasicResponse<UserInfo> userInfoBasicResponse = new BasicResponse<>();
        userInfoBasicResponse.setStatus(200);
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(id);
        userInfo.setFirstName("Rachita");
        userInfoBasicResponse.setResult(userInfo);
        Mockito.when(questInsightsConnector.getUserById(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(userInfoBasicResponse);
        BasicResponse<Boolean> deletePolicyResponse =  new BasicResponse<Boolean>().setStatus(200).setResult(true);
        Mockito.when(policyIntegrationConnector.deletePolicy(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(deletePolicyResponse);
        BasicResponse<List<PolicySummary>> policySummaryResponse = new BasicResponse<List<PolicySummary>>().setResult(Collections.EMPTY_LIST).setStatus(200);
        Product product = new Product();
        product.setName("HullandWar");
        BasicResponse<List<Product>> productResponse = new BasicResponse<List<Product>>().setResult(Arrays.asList(product)).setStatus(200);
        Mockito.when(questInsightsConnector.getProducts(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(productResponse);
        Mockito.when(policyIntegrationConnector.getPoliciesByAccountIdAndProductAndProductLine(Mockito.any(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(policySummaryResponse);
        BasicResponse<Boolean> deleteAccountResponse = new BasicResponse<Boolean>().setStatus(200).setResult(Boolean.FALSE);
        Mockito.when(questInsightsConnector.deleteAccountByAccountId(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(deleteAccountResponse);
        Boolean result=submissionMutationResolver.deleteSubmission(submissionMutation, deleteSubmissionRequest,dataFetchingEnvironment);
    }

    @Test(expected = GraphQLException.class)
    public void testDeleteSubmission_DeleteSubmissionFail() throws JSONException {
        String[] role = {};
        User user = new User("id", "questToken", new HashSet(new HashSet<>(Arrays.asList(role))) , true);
        Mockito.when(sentry.getLoggedInUser(dataFetchingEnvironment)).thenReturn(user);
        UserInfoResponse userInfoResponse = new UserInfoResponse();
        userInfoResponse.setId("id");
        Mockito.when(accessManagementSal.getUserInfo(Mockito.anyString(),Mockito.anyString())).thenReturn(userInfoResponse);
        DeleteSubmissionRequest deleteSubmissionRequest = new DeleteSubmissionRequest();
        String id = UUID.randomUUID().toString();
        deleteSubmissionRequest.setDeleteAssociatedAccount(true);
        deleteSubmissionRequest.setDeletionReason("test");
        deleteSubmissionRequest.setSubmissionId(id);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        SubmissionDetailResponse submission = getSubmissionDetailsResponse(id);
        BasicResponse<List<SubmissionDetailResponse>> response = new BasicResponse().setStatus(200).setResult(Arrays.asList(submission));
        Mockito.when(submissionManagerConnector.getSubmissionsByIds(Mockito.anyList(),Mockito.anyString(),Mockito.anyBoolean())).thenReturn(response);
        BasicResponse<UserInfo> userInfoBasicResponse = new BasicResponse<>();
        userInfoBasicResponse.setStatus(200);
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(id);
        userInfo.setFirstName("Rachita");
        userInfoBasicResponse.setResult(userInfo);
        Mockito.when(questInsightsConnector.getUserById(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(userInfoBasicResponse);
        BasicResponse<Boolean> deletePolicyResponse =  new BasicResponse<Boolean>().setStatus(200).setResult(true);
        Mockito.when(policyIntegrationConnector.deletePolicy(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(deletePolicyResponse);
        BasicResponse<List<PolicySummary>> policySummaryResponse = new BasicResponse<List<PolicySummary>>().setResult(Collections.EMPTY_LIST).setStatus(200);
        Product product = new Product();
        product.setName("HullandWar");
        BasicResponse<List<Product>> productResponse = new BasicResponse<List<Product>>().setResult(Arrays.asList(product)).setStatus(200);
        Mockito.when(questInsightsConnector.getProducts(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(productResponse);
        Mockito.when(policyIntegrationConnector.getPoliciesByAccountIdAndProductAndProductLine(Mockito.any(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(policySummaryResponse);
        BasicResponse<Boolean> deleteAccountResponse = new BasicResponse<Boolean>().setStatus(200).setResult(Boolean.TRUE);
        Mockito.when(questInsightsConnector.deleteAccountByAccountId(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(deleteAccountResponse);
        BasicResponse<Boolean> deleteSubmissionResponse = new BasicResponse<Boolean>().setStatus(200).setResult(Boolean.FALSE);
        Mockito.when(submissionManagerConnector.deleteSubmission(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(deleteSubmissionResponse);
        Boolean result=submissionMutationResolver.deleteSubmission(submissionMutation, deleteSubmissionRequest,dataFetchingEnvironment);
    }

    @Test(expected = GraphQLException.class)
    public void testDeleteSubmission_DeletePolicyFail() throws JSONException {
        String[] role = {};
        User user = new User("id", "questToken", new HashSet(new HashSet<>(Arrays.asList(role))) , true);
        Mockito.when(sentry.getLoggedInUser(dataFetchingEnvironment)).thenReturn(user);
        UserInfoResponse userInfoResponse = new UserInfoResponse();
        userInfoResponse.setId("id");
        Mockito.when(accessManagementSal.getUserInfo(Mockito.anyString(),Mockito.anyString())).thenReturn(userInfoResponse);
        DeleteSubmissionRequest deleteSubmissionRequest = new DeleteSubmissionRequest();
        String id = UUID.randomUUID().toString();
        deleteSubmissionRequest.setDeleteAssociatedAccount(true);
        deleteSubmissionRequest.setDeletionReason("test");
        deleteSubmissionRequest.setSubmissionId(id);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        SubmissionDetailResponse submission = getSubmissionDetailsResponse(id);
        BasicResponse<List<SubmissionDetailResponse>> response = new BasicResponse().setStatus(200).setResult(Arrays.asList(submission));
        Mockito.when(submissionManagerConnector.getSubmissionsByIds(Mockito.anyList(),Mockito.anyString(),Mockito.anyBoolean())).thenReturn(response);
        BasicResponse<UserInfo> userInfoBasicResponse = new BasicResponse<>();
        userInfoBasicResponse.setStatus(200);
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(id);
        userInfo.setFirstName("Rachita");
        userInfoBasicResponse.setResult(userInfo);
        Mockito.when(questInsightsConnector.getUserById(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(userInfoBasicResponse);
        BasicResponse<Boolean> deletePolicyResponse =  new BasicResponse<Boolean>().setStatus(200).setResult(false);
        Mockito.when(policyIntegrationConnector.deletePolicy(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(deletePolicyResponse);
        Boolean result=submissionMutationResolver.deleteSubmission(submissionMutation, deleteSubmissionRequest,dataFetchingEnvironment);
    }

    @Test(expected = GraphQLException.class)
    public void testDeleteSubmission_AccountAssociatedWithMorePolicies() throws JSONException {
        String[] role = {};
        User user = new User("id", "questToken", new HashSet(new HashSet<>(Arrays.asList(role))) , true);
        Mockito.when(sentry.getLoggedInUser(dataFetchingEnvironment)).thenReturn(user);
        UserInfoResponse userInfoResponse = new UserInfoResponse();
        userInfoResponse.setId("id");
        Mockito.when(accessManagementSal.getUserInfo(Mockito.anyString(),Mockito.anyString())).thenReturn(userInfoResponse);
        DeleteSubmissionRequest deleteSubmissionRequest = new DeleteSubmissionRequest();
        String id = UUID.randomUUID().toString();
        deleteSubmissionRequest.setDeleteAssociatedAccount(true);
        deleteSubmissionRequest.setDeletionReason("test");
        deleteSubmissionRequest.setSubmissionId(id);
        Mockito.when(commonUtil.getClientId(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        Mockito.when(commonUtil.getToken(Mockito.any())).thenReturn(UUID.randomUUID().toString());
        SubmissionDetailResponse submission = getSubmissionDetailsResponse(id);
        BasicResponse<List<SubmissionDetailResponse>> response = new BasicResponse().setStatus(200).setResult(Arrays.asList(submission));
        Mockito.when(submissionManagerConnector.getSubmissionsByIds(Mockito.anyList(),Mockito.anyString(),Mockito.anyBoolean())).thenReturn(response);
        BasicResponse<UserInfo> userInfoBasicResponse = new BasicResponse<>();
        userInfoBasicResponse.setStatus(200);
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(id);
        userInfo.setFirstName("Rachita");
        userInfoBasicResponse.setResult(userInfo);
        Mockito.when(questInsightsConnector.getUserById(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(userInfoBasicResponse);
        BasicResponse<Boolean> deletePolicyResponse =  new BasicResponse<Boolean>().setStatus(200).setResult(true);
        Mockito.when(policyIntegrationConnector.deletePolicy(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(deletePolicyResponse);
        PolicySummary policySummary = new PolicySummary();
        policySummary.setInsurancePolicyId(id);
        BasicResponse<List<PolicySummary>> policySummaryResponse = new BasicResponse<List<PolicySummary>>().setResult(Arrays.asList(policySummary)).setStatus(200);
        Product product = new Product();
        product.setName("HullandWar");
        BasicResponse<List<Product>> productResponse = new BasicResponse<List<Product>>().setResult(Arrays.asList(product)).setStatus(200);
        Mockito.when(questInsightsConnector.getProducts(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(productResponse);
        Mockito.when(policyIntegrationConnector.getPoliciesByAccountIdAndProductAndProductLine(Mockito.any(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(policySummaryResponse);
        BasicResponse<Boolean> deleteAccountResponse = new BasicResponse<Boolean>().setStatus(200).setResult(Boolean.TRUE);
        Mockito.when(questInsightsConnector.deleteAccountByAccountId(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(deleteAccountResponse);
        BasicResponse<Boolean> deleteSubmissionResponse = new BasicResponse<Boolean>().setStatus(200).setResult(Boolean.FALSE);
        Mockito.when(submissionManagerConnector.deleteSubmission(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(deleteSubmissionResponse);
        Boolean result=submissionMutationResolver.deleteSubmission(submissionMutation, deleteSubmissionRequest,dataFetchingEnvironment);
    }

}
