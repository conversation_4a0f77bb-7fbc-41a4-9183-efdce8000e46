package com.concirrus.submissions.common;

import com.concirrus.submissions.SubmissionGatewayApplication;
import com.concirrus.submissions.model.FleetSizeRange;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;

public class CommonConstants {

    public static final List<Integer> riskScoresList = Arrays.asList(1,2,3,4,5,6,7,8,9,10);

    // "1-100", "100-500", "500-999", "999+"
    public static final List<FleetSizeRange> fleetSizeFilter = Arrays.asList(new FleetSizeRange(1,100),new FleetSizeRange(100,500),new FleetSizeRange(500,999),new FleetSizeRange(999,null));

    public static final String SUBMISSION_DASHBOARD_FEATURE = "submission-dashboard";
    public static final String STATUS_ARCHIVE = "ARCHIVE";

    public static final Logger LOGGER = LoggerFactory.getLogger(SubmissionGatewayApplication.class);
    public static final String CLIENT_ID_HEADER = "client-id";
    public static final int MAX_CONTENT_LENGTH_3 = 3;
    public static final int MAX_CONTENT_LENGTH_100 = 100;

    private CommonConstants() {
    }
}
