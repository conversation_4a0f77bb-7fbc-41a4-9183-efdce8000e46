package com.concirrus.submissions.common;

import com.concirrus.submissions.model.repository.LicenceHash;
import com.concirrus.submissions.model.repository.hash.ClientFeatureHash;
import com.concirrus.submissions.repository.LicenceHashRepository;
import com.concirrus.submissions.repository.hash.ClientFeatureHashRepository;
import graphql.kickstart.servlet.context.GraphQLServletContext;
import graphql.schema.DataFetchingEnvironment;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.Optional;

@Component
public class CommonUtil {
    private final ClientFeatureHashRepository clientFeatureHashRepository;
    private final LicenceHashRepository licenceHashRepository;
    public static final String LOGGED_IN_USER_ID= "x-user-id";

    public CommonUtil(ClientFeatureHashRepository clientFeatureHashRepository,LicenceHashRepository licenceHashRepository) {
        this.clientFeatureHashRepository = clientFeatureHashRepository;
        this.licenceHashRepository = licenceHashRepository;
    }

    //Removed static to avoid power mock , might need to be looked on later.
    public String getClientId(DataFetchingEnvironment dataFetchingEnvironment) {
        GraphQLServletContext context = dataFetchingEnvironment.getContext();
        return context.getHttpServletRequest().getHeader("client-id");
    }

    public String getToken(DataFetchingEnvironment dataFetchingEnvironment) {
        GraphQLServletContext context = dataFetchingEnvironment.getContext();
        return context.getHttpServletRequest().getHeader(HttpHeaders.AUTHORIZATION);
    }

    public String getLoggedInUser(DataFetchingEnvironment dataFetchingEnvironment) {
        GraphQLServletContext context = dataFetchingEnvironment.getContext();
        return context.getHttpServletRequest().getHeader(LOGGED_IN_USER_ID);
    }

    public String createUsername(String firstName, String lastName) {
        String username = firstName;
        if (!StringUtils.isEmpty(lastName)) {
            username += " ".concat(lastName);
        }
        return username;
    }

    public boolean isFeatureEnabled(String clientId, String featureName) {
        Optional<LicenceHash> licenceHashOptional = licenceHashRepository.findById(clientId);
        //Checking if license if present for client and is not expired.
        final boolean isLicenseExpired = licenceHashOptional.map(licenceHash -> LocalDate.now().isAfter(licenceHash.getValidUntil())).orElse(true);
        if (!isLicenseExpired) {
            Optional<ClientFeatureHash> clientFeatureHashOptional = clientFeatureHashRepository.findById(clientId);
            return clientFeatureHashOptional.map(clientFeatureHash -> !CollectionUtils.isEmpty(clientFeatureHash.getAllowedFeatures()) && clientFeatureHash.getAllowedFeatures().contains(featureName)).orElse(false);
        } else {
            return false;
        }
    }
}
