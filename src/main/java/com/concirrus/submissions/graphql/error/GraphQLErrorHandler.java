package com.concirrus.submissions.graphql.error;

import graphql.ExceptionWhileDataFetching;
import graphql.GraphQLError;
import graphql.kickstart.execution.error.DefaultGraphQLErrorHandler;
import graphql.kickstart.execution.error.GenericGraphQLError;
import graphql.schema.CoercingParseValueException;
import graphql.validation.ValidationError;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.convert.ConversionFailedException;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;

@Component
public class GraphQLErrorHandler extends DefaultGraphQLErrorHandler {

    public static final String GET_EXCEPTION = "getException";
    public static final String GET_MESSAGE = "getMessage";
    public static final String GET_PARSED_STRING = "getParsedString";
    public static final String FIELD_NOT_FOUND = "FIELD NOT FOUND";
    public static final String METHOD_NOT_FOUND = "METHOD NOT FOUND";
    public static final String GET_VALUE = "getValue";
    public static final String GET_TARGET_TYPE = "getTargetType";
    public static final String GET_SIMPLE_NAME = "getSimpleName";
    public static final String GET_MOST_SPECIFIC_CAUSE = "getMostSpecificCause";

    private Logger log = LoggerFactory.getLogger(GraphQLErrorHandler.class);

    @Override
    public List<GraphQLError> processErrors(List<GraphQLError> errors) {
        List<GraphQLError> clientErrors = new ArrayList<>();
        if (!CollectionUtils.isEmpty(errors)) {
            errors.forEach(graphQLError -> {
                // use reflection to check if there is Exception and then getMessage
                // getMessage will always available in GraphQLError object
                if (graphQLError instanceof ExceptionWhileDataFetching) {
                    ExceptionWhileDataFetching exceptionWhileDataFetching = (ExceptionWhileDataFetching) graphQLError;
                    Throwable exception = exceptionWhileDataFetching.getException();
                    Throwable cause = exception.getCause();
                    if (exception instanceof NumberFormatException) {
                        Object errorMessage = invokeMethod(cause, GET_MESSAGE);
                        clientErrors.add(new GenericGraphQLError(String.format("Number Format Error : %s", errorMessage)));
                        return;
                    } else if (exception instanceof DateTimeParseException) {
                        Object errorMessage = invokeMethod(cause, GET_PARSED_STRING);
                        clientErrors.add(new GenericGraphQLError(String.format("Date Time Parse Error for %s ", errorMessage)));
                    } else if (exception instanceof ConversionFailedException) {
                        Object value = invokeMethod(cause, GET_VALUE);
                        Object targetType = invokeMethod(cause, GET_TARGET_TYPE);
                        Object simpleName = invokeMethod(targetType, GET_SIMPLE_NAME);
                        clientErrors.add(new GenericGraphQLError(String.format("Unable to convert input to type %s", simpleName)));
                        return;
                    } else if (exception instanceof HttpMessageNotReadableException) {
                        Throwable specificCause = ((HttpMessageNotReadableException) exception).getMostSpecificCause();
                        String errorMessage = "Invalid request" + " : " + specificCause.getMessage();
                        clientErrors.add(new GenericGraphQLError(errorMessage));
                        return;
                    } else if (exception instanceof IllegalArgumentException) {
                        Object value = invokeMethod(cause, GET_VALUE);
                        Object targetType = invokeMethod(cause, GET_TARGET_TYPE);
                        Object simpleName = invokeMethod(targetType, GET_SIMPLE_NAME);
                        clientErrors.add(new GenericGraphQLError(String.format("Unable to covert input to type %s", simpleName)));
                        return;
                    }
                    else {
                        clientErrors.add(new GenericGraphQLError(graphQLError.getErrorType() != null ? graphQLError.getErrorType().toString() : HttpStatus.INTERNAL_SERVER_ERROR.toString()));
                    }
                }
                else if(graphQLError instanceof ValidationError){
                    ValidationError validationError = (ValidationError) graphQLError;
                    clientErrors.add(new GenericGraphQLError(String.format("Validation Error :  %s", validationError.getValidationErrorType())));
                    return;
                }
                else if(graphQLError instanceof CoercingParseValueException)
                {
                    CoercingParseValueException coercingParseValueException = (CoercingParseValueException) graphQLError;
                    clientErrors.add(new GenericGraphQLError(String.format("Coercing Parse Value Error :  %s", coercingParseValueException.getErrorType())));
                    return;
                }
                else {
                    Object exception = invokeMethod(graphQLError, GET_EXCEPTION);
                    Object message = invokeMethod(exception, GET_MESSAGE);
                    clientErrors.add(new GenericGraphQLError(message.toString()));
                }
            });
        } else {
            clientErrors.add(new GenericGraphQLError(HttpStatus.INTERNAL_SERVER_ERROR.toString()));
        }
        return clientErrors;
    }

    /**
     * @param object the object to do the lookup
     * @param name   the field to access, preference in the given order
     * @return the field if available else the object itself
     */
    public Object getField(Object object, String name) {
        try {
            Field field = object.getClass().getDeclaredField(name);
            field.setAccessible(true);
            return field.get(object);
        } catch (Exception e) {
            log.error(FIELD_NOT_FOUND, e);
        }
        return object;
    }

    /**
     * @param object the object to do the lookup
     * @param name   the methods to access, preference in the given order
     * @return invoke the method if available else return the object itself
     */
    public Object invokeMethod(Object object, String name) {
        try {
            Method method = object.getClass().getMethod(name);
            method.setAccessible(true);
            return method.invoke(object);
        } catch (Exception e) {
            log.error(METHOD_NOT_FOUND, e);
        }
        return object;
    }

}
