package com.concirrus.submissions.graphql.error;

import org.springframework.lang.NonNull;

import java.util.HashMap;
import java.util.Map;


public class ClientMeaningfulFetchingException extends RuntimeException {

  private final String errorType;
  private final Map<String, Object> extensions = new HashMap<>();

  public ClientMeaningfulFetchingException(@NonNull String message, String errorType) {
    super(message);
    this.errorType = errorType;
  }

  public ClientMeaningfulFetchingException(@NonNull String message, String errorType, Map<String, Object> extensions) {
    this(message, errorType);
    this.extensions.putAll(extensions);
  }

  public String getErrorType() {
    return errorType;
  }

  public Map<String, Object> getExtensions() {
    return extensions;
  }

}
