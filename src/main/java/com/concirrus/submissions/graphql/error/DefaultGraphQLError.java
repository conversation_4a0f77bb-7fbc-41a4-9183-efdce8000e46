package com.concirrus.submissions.graphql.error;

import graphql.ErrorClassification;
import graphql.GraphQLError;
import graphql.language.SourceLocation;

import java.util.List;
import java.util.Map;

public class DefaultGraphQLError implements GraphQLError {
    private final String message;
    private final List<SourceLocation> locations;
    private final ErrorClassification errorType;
    private final List<Object> path;
    private final Map<String, Object> extensions;

    public DefaultGraphQLError(String message, List<SourceLocation> locations, ErrorClassification errorType, List<Object> path, Map<String, Object> extensions) {
        this.message = message;
        this.locations = locations;
        this.errorType = errorType;
        this.path = path;
        this.extensions = extensions;
    }

    public String getMessage() {
        return message;
    }

    public List<SourceLocation> getLocations() {
        return locations;
    }

    public ErrorClassification getErrorType() {
        return errorType;
    }

    @Override
    public List<Object> getPath() {
        return path;
    }

    @Override
    public Map<String, Object> getExtensions() {
        return extensions;
    }

}
