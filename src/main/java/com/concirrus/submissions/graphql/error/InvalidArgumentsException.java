package com.concirrus.submissions.graphql.error;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * <AUTHOR> VipinK
 */
@ResponseStatus(value= HttpStatus.NOT_ACCEPTABLE, reason="Invalid arguments")
public class InvalidArgumentsException extends RuntimeException{

    private static final String DEFAULT_MESSAGE = "Invalid arguments";

    public InvalidArgumentsException(String message){
        super(message);
    }

    public InvalidArgumentsException(){
        super(DEFAULT_MESSAGE);
    }

    public InvalidArgumentsException(Throwable cause){
        super(cause);
    }
}
