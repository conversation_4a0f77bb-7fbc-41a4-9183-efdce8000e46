package com.concirrus.submissions.graphql.error;

import graphql.ExceptionWhileDataFetching;
import graphql.GraphQLError;
import graphql.kickstart.execution.error.GraphQLErrorHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletionException;
import java.util.stream.Collectors;

public class SpecificGraphQLErrorHandler implements GraphQLErrorHandler {

	private final static Logger log = LoggerFactory.getLogger(SpecificGraphQLErrorHandler.class);

	@Override
	public List<GraphQLError> processErrors(List<GraphQLError> errors) {
		String errorsStr = errors.stream()
				.map(it -> String.format("%s %s %s", it.getErrorType(), it.getMessage(), it.getLocations()))
				.collect(Collectors.joining(", "));
		log.error(errorsStr);

		return errors.stream().map(this::handleError).collect(Collectors.toList());
	}

	private GraphQLError handleError(GraphQLError error) {
		if (isClientError(error)) {
			return error;
		} else {
			return extractClientMeaningfulFetchingException(error)
					.map(e -> createClientMeaningfulGraphQLError(error, e))
					.orElseGet(() -> createInternalServerGraphQLError(error));
		}
	}

	private boolean isClientError(GraphQLError error) {
		return !(error instanceof ExceptionWhileDataFetching || error instanceof Throwable);
	}

	private static Optional<ClientMeaningfulFetchingException> extractClientMeaningfulFetchingException(
			GraphQLError error) {
		if (error instanceof ExceptionWhileDataFetching) {
			Throwable exception = ((ExceptionWhileDataFetching) error).getException();
			if (exception instanceof CompletionException) {
				exception = exception.getCause();
			}
			if (exception instanceof ClientMeaningfulFetchingException) {
				return Optional.of((ClientMeaningfulFetchingException) exception);
			}
		}
		return Optional.empty();
	}

	private static GraphQLError createClientMeaningfulGraphQLError(GraphQLError error,
			ClientMeaningfulFetchingException clientMeaningfulFetchingException) {
		return new DefaultGraphQLError(clientMeaningfulFetchingException.getMessage(), error.getLocations(),
				error.getErrorType(), error.getPath(),
				createClientMeaningfulErrorExtensions(clientMeaningfulFetchingException));
	}

	private static Map<String, Object> createClientMeaningfulErrorExtensions(
			ClientMeaningfulFetchingException clientMeaningfulFetchingException) {
		Map<String, Object> extensions = new HashMap<>();
		extensions.put("errorType", clientMeaningfulFetchingException.getErrorType());
		extensions.putAll(clientMeaningfulFetchingException.getExtensions());
		return extensions;
	}

	private static GraphQLError createInternalServerGraphQLError(GraphQLError error) {
		return new DefaultGraphQLError("Internal server error", error.getLocations(), error.getErrorType(),
				error.getPath(), null);
	}

}
