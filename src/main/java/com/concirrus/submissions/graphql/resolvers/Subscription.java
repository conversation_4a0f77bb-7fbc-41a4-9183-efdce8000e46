package com.concirrus.submissions.graphql.resolvers;

import com.concirrus.submission.connector.submissionmanager.model.SubmissionState;
import com.concirrus.submissions.integration.AviationSubmissionChangesService;
import com.concirrus.submissions.integration.AviationWarSubmissionChangeService;
import com.concirrus.submissions.integration.ConstructionSubmissionChangesService;
import com.concirrus.submissions.integration.SubmissionChangesService;
import com.concirrus.submissions.model.SubmissionsChange;
import com.concirrus.submissions.model.aviation.AviationSubmissionsChange;
import com.concirrus.submissions.model.aviationwar.AviationWarJobUpdate;
import com.concirrus.submissions.model.aviationwar.AviationWarSubmissionChange;
import com.concirrus.submissions.model.construction.ConstructionSubmissionsChange;
import com.concirrus.submissionservice.connector.submissionhandler.model.ProductType;
import graphql.kickstart.tools.GraphQLSubscriptionResolver;
import org.reactivestreams.Publisher;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
public class Subscription implements GraphQLSubscriptionResolver {
    SubmissionChangesService submissionChangesService;
    AviationSubmissionChangesService aviationSubmissionChangesService;

    ConstructionSubmissionChangesService constructionSubmissionChangesService;
    AviationWarSubmissionChangeService aviationWarSubmissionChangeService;

    public Subscription(SubmissionChangesService submissionChangesService, AviationSubmissionChangesService aviationSubmissionChangesService, ConstructionSubmissionChangesService constructionSubmissionChangesService, AviationWarSubmissionChangeService aviationWarSubmissionChangeService ) {
        this.submissionChangesService = submissionChangesService;
        this.aviationSubmissionChangesService = aviationSubmissionChangesService;
        this.constructionSubmissionChangesService = constructionSubmissionChangesService;
        this.aviationWarSubmissionChangeService = aviationWarSubmissionChangeService;
    }

    public Publisher<Long> submissionsItemCount(String clientId, String submissionState, String type) {
        if(StringUtils.hasText(type) && type.equalsIgnoreCase("AVIATION")){
            return aviationSubmissionChangesService.getSubmissionItemCountPublisher(clientId, submissionState, ProductType.valueOf(type));
        } else if (StringUtils.hasText(type) && type.equalsIgnoreCase(ProductType.CONSTRUCTION.name())) {
            return constructionSubmissionChangesService.getSubmissionItemCountPublisher(clientId,submissionState, ProductType.valueOf(type));
        } else if (StringUtils.hasText(type) && type.equalsIgnoreCase(ProductType.AVIATION_WAR.name())){
            return aviationWarSubmissionChangeService.getSubmissionItemCountPublisher(clientId,submissionState,ProductType.valueOf(type));
        }
        return submissionChangesService.getSubmissionItemCountPublisher(clientId,submissionState);
    }

    public Publisher<SubmissionsChange> submissionChanges(String clientId) {
        return submissionChangesService.getSubmissionChanges(clientId);
    }

    public Publisher<AviationSubmissionsChange> aviationSubmissionChanges(String clientId) {
        return aviationSubmissionChangesService.getSubmissionChanges(clientId);
    }

    public Publisher<ConstructionSubmissionsChange> constructionSubmissionChanges(String clientId) {
        return constructionSubmissionChangesService.getSubmissionChanges(clientId);
    }
    public Publisher<AviationWarSubmissionChange> aviationWarSubmissionChanges(String clientId) {
        return aviationWarSubmissionChangeService.getSubmissionChanges(clientId);
    }
}
