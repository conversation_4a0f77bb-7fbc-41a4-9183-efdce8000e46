package com.concirrus.submissions.graphql.resolvers;

import com.concirrus.quest.common.rest.model.BasicResponse;
import com.concirrus.submission.connector.workflowmanager.WorkflowManagerConnector;
import com.concirrus.submission.connector.workflowmanager.model.SubmissionStateResponse;
import com.concirrus.submission.connector.workflowmanager.model.SubmissionStatusResponse;
import com.concirrus.submissions.common.CommonConstants;
import com.concirrus.submissions.common.CommonUtil;
import com.concirrus.submissions.connector.questinsights.QuestInsightsConnector;
import com.concirrus.submissions.connector.questinsights.model.referencedata.Product;
import com.concirrus.submissions.graphql.model.ReferenceData;
import com.concirrus.submissions.model.FleetSizeRange;
import graphql.GraphQLException;
import graphql.kickstart.tools.GraphQLResolver;
import graphql.schema.DataFetchingEnvironment;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.concirrus.submissions.common.CommonConstants.STATUS_ARCHIVE;

@Component
public class ReferenceDataQueryResolver implements GraphQLResolver<ReferenceData> {

    QuestInsightsConnector questInsightsConnector ;

    WorkflowManagerConnector workflowManagerConnector;

    CommonUtil commonUtil;

    public ReferenceDataQueryResolver(QuestInsightsConnector questInsightsConnector, WorkflowManagerConnector workflowManagerConnector, CommonUtil commonUtil) {
        this.questInsightsConnector = questInsightsConnector;
        this.workflowManagerConnector = workflowManagerConnector;
        this.commonUtil = commonUtil;
    }

    public List<SubmissionStateResponse> submissionStates(ReferenceData referenceData, DataFetchingEnvironment environment) {
        BasicResponse<List<SubmissionStateResponse>> response = workflowManagerConnector.getAllSubmissionStates();
        if (HttpStatus.OK.value() == response.getStatus()) {
            return response.getResult();
        } else {
            throw new GraphQLException((String) response.getError());
        }
    }

    public List<SubmissionStatusResponse> submissionStatus(ReferenceData referenceData, DataFetchingEnvironment environment) {
        BasicResponse<List<SubmissionStatusResponse>> response = workflowManagerConnector.getAllSubmissionStatuses();
        if (HttpStatus.OK.value() == response.getStatus()) {
            return response.getResult();
        } else {
            throw new GraphQLException((String) response.getError());
        }
    }

    public List<String> statusComments(ReferenceData referenceData, String status, DataFetchingEnvironment environment) {
        BasicResponse<List<String>> response = workflowManagerConnector.getSubmissionRejectionReasons(status);
        if (HttpStatus.OK.value() == response.getStatus()) {
            return response.getResult();
        } else {
            throw new GraphQLException((String) response.getError());
        }
    }

    public List<Product> products(ReferenceData referenceData, String productLineId, DataFetchingEnvironment environment) {
        BasicResponse<List<Product>> response = questInsightsConnector.getProducts(productLineId, commonUtil.getToken(environment), commonUtil.getClientId(environment));
        if (HttpStatus.OK.value() == response.getStatus()) {
            return response.getResult();
        } else {
            throw new GraphQLException((String) response.getError());
        }

    }

    public List<Integer> riskScores(ReferenceData referenceData, DataFetchingEnvironment environment) {
        return CommonConstants.riskScoresList;
    }

    public List<FleetSizeRange> fleetSizes(ReferenceData referenceData, DataFetchingEnvironment environment) {
        return CommonConstants.fleetSizeFilter;
    }

    public List<String> submissionDeletionReasons(ReferenceData referenceData, DataFetchingEnvironment environment) {
        BasicResponse<List<String>> response = workflowManagerConnector.getSubmissionRejectionReasons(STATUS_ARCHIVE);
        if (HttpStatus.OK.value() == response.getStatus()) {
            return response.getResult();
        } else {
            throw new GraphQLException((String) response.getError());
        }
    }
}


