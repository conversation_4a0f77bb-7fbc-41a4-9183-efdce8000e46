package com.concirrus.submissions.graphql.resolvers;

import com.concirrus.quest.common.rest.exception.NotFoundException;
import com.concirrus.quest.common.rest.model.BasicResponse;
import com.concirrus.submission.common.validator.ContentValidator;
import com.concirrus.submission.connector.accessmanagement.model.UserInfoResponse;
import com.concirrus.submission.connector.accessmanagement.model.UserResponseDto;
import com.concirrus.submission.connector.policyintegration.PolicyIntegrationConnector;
import com.concirrus.submission.connector.submissionmanager.SubmissionManagerConnector;
import com.concirrus.submission.connector.submissionmanager.model.Submission;
import com.concirrus.submission.connector.submissionmanager.model.SubmissionDetailResponse;
import com.concirrus.submission.connector.submissionmanager.model.SubmissionState;
import com.concirrus.submission.service.notes.connector.notes.NotesServiceConnector;
import com.concirrus.submissions.common.CommonConstants;
import com.concirrus.submissions.common.CommonUtil;
import com.concirrus.submissions.connector.questinsights.QuestInsightsConnector;
import com.concirrus.submissions.connector.questinsights.model.account.Account;
import com.concirrus.submissions.connector.questinsights.model.referencedata.Product;
import com.concirrus.submissions.connector.questinsights.model.user.UserInfo;
import com.concirrus.submissions.graphql.model.EntityType;
import com.concirrus.submissions.graphql.model.LicenceQuery;
import com.concirrus.submissions.graphql.model.Note;
import com.concirrus.submissions.graphql.model.ReferenceData;
import com.concirrus.submissions.integration.AccessManagementSal;
import com.concirrus.submissions.model.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import graphql.GraphQLException;
import graphql.kickstart.tools.GraphQLQueryResolver;
import graphql.schema.DataFetchingEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class Query implements GraphQLQueryResolver {
    private static final Logger logger = LoggerFactory.getLogger(Query.class);

    private SubmissionManagerConnector submissionManagerConnector;

    private QuestInsightsConnector questInsightsConnector;

    private NotesServiceConnector notesServiceConnector;

    private ObjectMapper objectMapper;

    private CommonUtil commonUtil;

    private PolicyIntegrationConnector policyIntegrationConnector;

    AccessManagementSal accessManagementSal;

    public ReferenceData referenceData() {
        return new ReferenceData();
    }

    public LicenceQuery licence() {
        return new LicenceQuery();
    }

    public Query(SubmissionManagerConnector submissionManagerConnector, ObjectMapper objectMapper, CommonUtil commonUtil,NotesServiceConnector notesServiceConnector,PolicyIntegrationConnector policyIntegrationConnector,QuestInsightsConnector questInsightsConnector,AccessManagementSal accessManagementSal) {
        this.submissionManagerConnector = submissionManagerConnector;
        this.objectMapper = objectMapper;
        this.commonUtil = commonUtil;
        this.notesServiceConnector = notesServiceConnector;
        this.policyIntegrationConnector = policyIntegrationConnector;
        this.questInsightsConnector = questInsightsConnector;
        this.accessManagementSal = accessManagementSal;
    }

    public List<SubmissionDetail> submissionsSearch(SubmissionSearchRequest searchRequest, String productLine, String personaId, Page page, Sort sort, DataFetchingEnvironment environment) {
        CommonConstants.LOGGER.debug("Entering method submissionsSearch");
        CommonConstants.LOGGER.info("Searching submissions for productLine : {} and personaId : {}", productLine, personaId);

        ContentValidator.validate(searchRequest);
        ContentValidator.validate(sort);

        Instant startTime = Instant.now();
        logger.debug("Started user fetching {}", startTime);
        String authToken = commonUtil.getToken(environment);
        String clientId = commonUtil.getClientId(environment);
        Map<String, User> userIdUserMap = getAllUsersMap(clientId, authToken);
        Instant endTime = Instant.now();
        logger.debug("Ended user fetching {}", endTime);
        Duration duration = Duration.between(startTime, endTime);
        logger.debug("Time taken in user fetching {}", duration.toMillis());

        // searching users
        List<String> searchedUserIds = new ArrayList<>();
        if (searchRequest!=null && !StringUtils.isEmpty(searchRequest.getSearchText())) {
            searchedUserIds = userIdUserMap.values().stream()
                    .filter(user -> commonUtil.createUsername(user.getFirstName(), user.getLastName())
                            .toLowerCase()
                            .contains(
                                    searchRequest.getSearchText()
                                            .toLowerCase()
                            )
                    )
                    .map(User::getUserId)
                    .collect(Collectors.toList());
        }

        Instant startTime2 = Instant.now();
        logger.debug("Started submission fetching {}", startTime);
        BasicResponse<List<Submission>> submissionsResponse = submissionManagerConnector.searchSubmissions(SubmissionSearchRequest.getSearchRequestForQuest(searchRequest, searchedUserIds), clientId, page!=null ?page.getPageIndex():null, page!=null?page.getPageSize(): null, sort!=null?sort.getSortBy():null,sort!=null? sort.getSortOrder().toString(): null);
        Instant endTime2 = Instant.now();
        logger.debug("Ended submission fetching {}", endTime);
        Duration duration2 = Duration.between(startTime2, endTime2);
        logger.debug("Time taken in submission fetching {}", duration2.toMillis());

        if (HttpStatus.OK.value() == submissionsResponse.getStatus()) {
            Instant startTime1 = Instant.now();
            logger.debug("Started submission mapping {}", startTime);

            List<SubmissionDetail> submissionDetails = new ArrayList<>();
            if(CollectionUtils.isEmpty(submissionsResponse.getResult()))
            {
                CommonConstants.LOGGER.info("Submissions list is empty.");
                return Collections.emptyList();
            }

            for (Submission submission : submissionsResponse.getResult()) {
                SubmissionDetail submissionDetail = SubmissionDetail.getInstance(submission, userIdUserMap.get(submission.getAssigneeId()), submission.getRiskInsight());
//                List<VesselInfo> vessels = submissionDetail.getVessels();
//                List<String> imos = vessels.stream()
//                        .map(VesselInfo::getImo)
//                        .collect(Collectors.toList());
//                long nonImoCount = vessels.parallelStream()
//                        .filter(vessel -> Objects.nonNull(vessel.getNonImo()) && vessel.getNonImo())
//                        .count();
//
//                long unregisteredImoCount = vessels.parallelStream()
//                        .filter(vessel -> (Objects.isNull(vessel.getNonImo()) || !vessel.getNonImo()) && !StringUtils.hasText(vessel.getImo()))
//                        .count();
//                BasicResponse<Map<String, VesselValidationResponse>> validateResponse = questInsightsConnector.validateVessels(imos, authToken, clientId);
//                if (HttpStatus.OK.value() == validateResponse.getStatus()) {
//                    Map<String, VesselValidationResponse> validationResults = validateResponse.getResult();
//                    unregisteredImoCount += imos.stream()
//                            .filter(imo -> validationResults.containsKey(imo) && validationResults.get(imo).getVesselStatus().equals(VesselStatus.INVALID))
//                            .count();
//                }
//
//                submissionDetail.setNonImoCount((int) nonImoCount);
//                submissionDetail.setUnregisteredImoCount((int) unregisteredImoCount);
                submissionDetails.add(submissionDetail);
            }

            Instant endTime1 = Instant.now();
            logger.debug("Ended submission mapping {}", endTime);

            Duration duration1 = Duration.between(startTime1, endTime1);
            logger.debug("Time taken in submission mapping {}", duration1.toMillis());
            return submissionDetails;
        } else {
            Object error = submissionsResponse.getError();
            CommonConstants.LOGGER.error("Exception while fetching submissions from submission manager service : {}", error);
            throw new GraphQLException("Exception while fetching submissions from submission manager service");
        }
    }

    public List<SubmissionDetail> submissions(Boolean showArchived, LocalDate startDate, LocalDate endDate, String state, String productLine, String personaId, Page page, Sort sort, DataFetchingEnvironment dataFetchingEnvironment) {
        CommonConstants.LOGGER.info("Searching submissions by date range and state");

        ContentValidator.validate(sort);

        List<SubmissionDetail> submissionDetails = new ArrayList<>();
        Instant startDateInstant = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant();
        Instant endDateInstant = null;
        if (startDate.isEqual(endDate)) {
            endDateInstant = endDate.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant();
        } else {
            endDateInstant = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant();
        }
        BasicResponse<List<Submission>> response = submissionManagerConnector.getAllSubmissionsByStateAndDateRange(startDateInstant, endDateInstant, state!=null?SubmissionState.valueOf(state):null, commonUtil.getClientId(dataFetchingEnvironment), page!=null ?page.getPageIndex():null, page!=null?page.getPageSize(): null, sort!=null?sort.getSortBy():null,sort!=null? sort.getSortOrder().toString(): null, showArchived);
        if (HttpStatus.OK.value() == response.getStatus()) {
            List<Submission> submissionsList = response.getResult();
            if(submissionsList.isEmpty())
            {
                CommonConstants.LOGGER.info("Submissions list is empty.");
                return Collections.emptyList();
            }
            Map<String, User> userIdUserMap = getAllUsersMap(commonUtil.getClientId(dataFetchingEnvironment), commonUtil.getToken(dataFetchingEnvironment));
            for (Submission submission : submissionsList) {
                submissionDetails.add(SubmissionDetail.getInstance(submission, userIdUserMap.get(submission.getAssigneeId()), submission.getRiskInsight()));
            }
            return submissionDetails;
        }
        throw new GraphQLException("Error while fetching submissions by date range and state");
    }

    public Map<String, User> getAllUsersMap(String clientId, String token) {
        CommonConstants.LOGGER.info("Get all users for clientId : {}", clientId);
        BasicResponse<List<UserInfo>> userInfoResponse = questInsightsConnector.getUsers(token, clientId);
        if (Objects.isNull(userInfoResponse) || HttpStatus.OK.value() != userInfoResponse.getStatus()) {
            Object error = userInfoResponse!=null ? userInfoResponse.getError() : null;
            CommonConstants.LOGGER.error("Error while fetching users from Quest : {}", error);
            return Collections.emptyMap();
        }
        List<UserInfo> usersList = userInfoResponse.getResult();
        CommonConstants.LOGGER.info("Count of users obtained from Quest : {}", usersList.size());
        Map<String, User> userIdUserMap = new HashMap<>();
        for (UserInfo user : usersList) {
            userIdUserMap.put(user.getUserId(), User.getInstance(user));
        }
        return userIdUserMap;
    }

    public List<com.concirrus.submissions.graphql.model.Submission> submissionsByIds(List<String> ids, String productLine, Boolean showArchived, String personaId, DataFetchingEnvironment dataFetchingEnvironment) {
        CommonConstants.LOGGER.info("Searching submissions by submissionIds {}", ids);
        List<com.concirrus.submissions.graphql.model.Submission> submissionDetails = new ArrayList<>();
        BasicResponse<List<SubmissionDetailResponse>> response = submissionManagerConnector.getSubmissionsByIds(ids, commonUtil.getClientId(dataFetchingEnvironment), showArchived);
        if (HttpStatus.OK.value() == response.getStatus()) {
            List<SubmissionDetailResponse> submissionsList = response.getResult();
            Map<String, User> userMap = getAllUsersMap(commonUtil.getClientId(dataFetchingEnvironment), commonUtil.getToken(dataFetchingEnvironment));
            for (SubmissionDetailResponse submission : submissionsList) {
                submissionDetails.add(com.concirrus.submissions.graphql.model.Submission.from(submission, userMap.get(submission.getAssigneeId()), objectMapper));
            }
            return submissionDetails;
        }
        throw new GraphQLException("Error while fetching submission by Ids.");
    }


    public Long countSubmissionsByState(SubmissionState submissionState, Boolean showArchived, DataFetchingEnvironment environment) {
        String clientId =commonUtil.getClientId(environment);
        CommonConstants.LOGGER.info("Get submissions count for clientId : {} and  submissionState : {}", clientId, submissionState);
        BasicResponse<Long> response = submissionManagerConnector.getSubmissionCountByState(clientId, submissionState, showArchived);
        if (HttpStatus.OK.value() == response.getStatus()) {
            return response.getResult();
        } else {
            CommonConstants.LOGGER.error("Error in getting submissions count for clientId : {} and  submissionState : {} due to : {}", clientId, submissionState, response.getError());
            throw new GraphQLException(String.format("Error fetching submission count for state %s",submissionState));
        }
    }

    // Required as per SUB-587
    public Long countSearchedSubmissions(SubmissionSearchRequest searchRequest, DataFetchingEnvironment environment) {

        ContentValidator.validate(searchRequest);

        String clientId = commonUtil.getClientId(environment);
        String token = commonUtil.getToken(environment);
        CommonConstants.LOGGER.info("Get submissions count for clientId : {} and  searchRequest : {}", clientId, SubmissionSearchRequest.getSearchRequestForQuest(searchRequest));
        // Added fix as per SUB-1425
        Map<String, User> userIdUserMap = getAllUsersMap(clientId, token);
        // searching users
        List<String> searchedUserIds = new ArrayList<>();
        if (!StringUtils.isEmpty(searchRequest.getSearchText())) {
            searchedUserIds = userIdUserMap.values().stream()
                    .filter(user -> commonUtil.createUsername(user.getFirstName(), user.getLastName())
                            .toLowerCase()
                            .contains(
                                    searchRequest.getSearchText()
                                            .toLowerCase()
                            )
                    )
                    .map(User::getUserId)
                    .collect(Collectors.toList());
        }
        BasicResponse<Long> response = submissionManagerConnector.countSearchedSubmissions(SubmissionSearchRequest.getSearchRequestForQuest(searchRequest, searchedUserIds), clientId);
        if (HttpStatus.OK.value() == response.getStatus()) {
            return response.getResult();
        } else {
            CommonConstants.LOGGER.error("Error in getting submissions count for clientId : {} and  searchRequest : {} due to : {}", clientId, SubmissionSearchRequest.getSearchRequestForQuest(searchRequest), response.getError());
            throw new GraphQLException("Error while fetching submission count by search request");
        }
    }

    public List<User> users(List<String> userIds, DataFetchingEnvironment dataFetchingEnvironment) {
        BasicResponse<List<UserInfo>> userInfoResponse = questInsightsConnector.getUsers(commonUtil.getToken(dataFetchingEnvironment),commonUtil.getClientId(dataFetchingEnvironment));
        if (Objects.isNull(userInfoResponse) || HttpStatus.OK.value() != userInfoResponse.getStatus()) {
            Object error = userInfoResponse!=null ? userInfoResponse.getError() : null;
            CommonConstants.LOGGER.error("Error while fetching users from Quest : {}", error);
            return Collections.emptyList();
        }
        List<UserInfo> usersList = userInfoResponse.getResult();
        CommonConstants.LOGGER.info("Count of users obtained from Quest : {}", usersList.size());
        List<User> userList = new ArrayList<>();
        for (UserInfo user : usersList) {
            userList.add(User.getInstance(user));
        }
        return userList;
    }

    public List<Note> notes(String entityId, EntityType entityType, List<String> noteTypes, List<String> subEntityTypes, List<String> subEntityIds, DataFetchingEnvironment dataFetchingEnvironment) {
        String clientId = commonUtil.getClientId(dataFetchingEnvironment);
        List<com.concirrus.submission.service.notes.connector.notes.model.Note> noteList = notesServiceConnector.getNotesBySubmissionId(entityId, entityType.name(), "test", clientId, false, noteTypes, subEntityTypes, subEntityIds).getResult();
        if (Objects.isNull(noteList)) {
            throw new NotFoundException("Submission not found");
        }
        List<Note> notes = new ArrayList<>();
        List<UserResponseDto> users = accessManagementSal.getAllUsers(clientId, commonUtil.getToken(dataFetchingEnvironment));
        Map<String, User> userMap = new HashMap<>();
        for (UserResponseDto user : users) {
            userMap.put(user.getUserId(), User.getInstance(user));
        }
        for (com.concirrus.submission.service.notes.connector.notes.model.Note note :
                noteList) {
            User user = null;
            User updatedByUser = null;
            if (userMap.containsKey(note.getUserId())) {
                user = userMap.get(note.getUserId());
            }
            if (userMap.containsKey(note.getEditorUserId())) {
                updatedByUser = userMap.get(note.getEditorUserId());
            }

            notes.add(new Note(note.getId(), user, note.getTime().toString(), note.getText(),note.getNoteType(), note.getSubEntityId(), note.getSubEntityType(), note.getEditTime(), note.getEditorUserId(), updatedByUser));
        }
        return notes;
    }

    public List<com.concirrus.submissions.graphql.model.PolicySummary> policiesForAccountBySubmissionId(String submissionId, DataFetchingEnvironment dataFetchingEnvironment) {
        String clientId = commonUtil.getClientId(dataFetchingEnvironment);
        String token = commonUtil.getToken(dataFetchingEnvironment);
        ArrayList<com.concirrus.submissions.graphql.model.PolicySummary> policySummaries = new ArrayList<>();
        //Fetch submission Id and validate against client
        BasicResponse<List<SubmissionDetailResponse>> submissionBasicResponse = submissionManagerConnector.getSubmissionsByIds(Arrays.asList(submissionId), clientId, Boolean.FALSE);
        if (HttpStatus.OK.value() == submissionBasicResponse.getStatus() && !CollectionUtils.isEmpty(submissionBasicResponse.getResult())) {
            SubmissionDetailResponse submissionDetailResponse =submissionBasicResponse.getResult().get(0);
            Map<String, Object> policyInfo = com.concirrus.submissions.graphql.model.Submission.getPolicyInfoMap(submissionBasicResponse.getResult().get(0), objectMapper);
            String accountId = "";
            if (policyInfo.containsKey("counterPartyId")) {
                 accountId = policyInfo.get("counterPartyId").toString();

            } else {
                if(submissionDetailResponse.getAccountName()!=null) {
                    BasicResponse<Account> accountBasicResponse = questInsightsConnector.getAccountByName(submissionDetailResponse.getAccountName().getValue(), token, clientId);
                    if (accountBasicResponse != null && HttpStatus.OK.value() == accountBasicResponse.getStatus() && accountBasicResponse.getResult() != null) {
                         accountId = accountBasicResponse.getResult().getId();
                    }
                }else {
                    logger.error("No account exist for submission id : {} ", submissionId);
                    throw new GraphQLException(String.format("No account exist for submission id : %s", submissionId));
                }
            }
            BasicResponse<List<Product>> productListResponse = questInsightsConnector.getProducts(submissionDetailResponse.getProductLine(), token, clientId);
            String productList = submissionDetailResponse.getProductName();
            if(HttpStatus.OK.value() == productListResponse.getStatus() && CollectionUtils.isEmpty(productListResponse.getResult())){
                List<String> productName1List = productListResponse.getResult().stream().map(Product::getName).collect(Collectors.toList());
                productList = String.join(",", productName1List);
            }
            CommonConstants.LOGGER.info("Available product {}", productList);
            BasicResponse<List<com.concirrus.submission.connector.policyintegration.model.PolicySummary>> policySummaryResponse = policyIntegrationConnector.getPoliciesByAccountIdAndProductAndProductLine(clientId, token, accountId, productList, submissionDetailResponse.getProductLine());
            if (HttpStatus.OK.value() == policySummaryResponse.getStatus()) {
                List<com.concirrus.submission.connector.policyintegration.model.PolicySummary> policySummaryList = policySummaryResponse.getResult();
                for (com.concirrus.submission.connector.policyintegration.model.PolicySummary policySummary : policySummaryList) {
                    policySummaries.add(com.concirrus.submissions.graphql.model.PolicySummary.from(policySummary));
                }
            } else {
                logger.error("Error while fetching policies for account id : {} due to : {}", accountId, policySummaryResponse.getError());
                throw new GraphQLException(String.format("Error while fetching policies for account id : %s due to : %s",accountId, policySummaryResponse.getError()));
            }
        } else {
            logger.error("Submission {} doesn't exist due to {}", submissionId, submissionBasicResponse.getError());
            throw new GraphQLException(String.format("Submission %s doesn't exist", submissionId));
        }
        return policySummaries;
    }

    public User me(DataFetchingEnvironment dataFetchingEnvironment) {
        String clientId = commonUtil.getClientId(dataFetchingEnvironment);
        UserInfoResponse userInfo =accessManagementSal.getUserInfo(clientId,commonUtil.getToken(dataFetchingEnvironment));
        return User.getInstance(userInfo);
    }
}
