package com.concirrus.submissions.graphql.resolvers;

import com.concirrus.quest.common.rest.model.BasicResponse;
import com.concirrus.submission.connector.accessmanagement.AccessManagementConnector;
import com.concirrus.submission.connector.accessmanagement.model.SubmissionClientConfigDTO;
import com.concirrus.submission.connector.accessmanagement.model.UserInfoResponse;
import com.concirrus.submission.connector.workflowmanager.WorkflowManagerConnector;
import com.concirrus.submission.connector.workflowmanager.model.SubmissionAssigneeEntity;
import com.concirrus.submissions.common.CommonConstants;
import com.concirrus.submissions.common.CommonUtil;
import com.concirrus.submissions.connector.questinsights.QuestInsightsConnector;
import com.concirrus.submissions.connector.questinsights.model.user.UserInfo;
import com.concirrus.submissions.graphql.model.NotesMutations;
import com.concirrus.submissions.graphql.model.SubmissionMutation;
import com.concirrus.submissions.integration.AccessManagementSal;
import graphql.GraphQLException;
import graphql.kickstart.tools.GraphQLMutationResolver;
import graphql.schema.DataFetchingEnvironment;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
public class Mutation implements GraphQLMutationResolver {

    private final AccessManagementConnector accessManagementConnector;

    private final AccessManagementSal accessManagementSal;

    WorkflowManagerConnector workflowManagerConnector;

    private final CommonUtil commonUtil;;

    private final QuestInsightsConnector questInsightsConnector;

    public Mutation(AccessManagementConnector accessManagementConnector, AccessManagementSal accessManagementSal, CommonUtil commonUtil, QuestInsightsConnector questInsightsConnector, WorkflowManagerConnector workflowManagerConnector) {
        this.accessManagementConnector = accessManagementConnector;
        this.accessManagementSal = accessManagementSal;
        this.commonUtil = commonUtil;
        this.questInsightsConnector = questInsightsConnector;
        this.workflowManagerConnector = workflowManagerConnector;
    }


    public SubmissionMutation submission(DataFetchingEnvironment environment)
   {
       return new SubmissionMutation();
   }

    public NotesMutations notes(DataFetchingEnvironment environment) {
        return new NotesMutations();
    }

    public SubmissionClientConfigDTO updateClientConfig(String clientId, boolean enrichment , DataFetchingEnvironment dataFetchingEnvironment) {
        UserInfoResponse userInfo =accessManagementSal.getUserInfo(clientId,commonUtil.getToken(dataFetchingEnvironment));
        CommonConstants.LOGGER.info("Updating Client enrichment config : {}", clientId);
        this.validateUser(userInfo.getId(), dataFetchingEnvironment);
        BasicResponse<SubmissionClientConfigDTO> response = accessManagementConnector.updateSubmissionClientConfig(clientId, enrichment);
        if (HttpStatus.OK.value() == response.getStatus()) {
            if (userInfo !=null) {
                CommonConstants.LOGGER.info("Updating Client enrichment config : {} by user : {}", clientId, userInfo.getEmail());
            }
            return response.getResult();
        } else {
            CommonConstants.LOGGER.error("Error in updating client enrichment config : {} due to : {}", clientId, response.getError());
            throw new GraphQLException(String.format("Error updating client enrichment config %s",clientId));
        }
    }

    public SubmissionClientConfigDTO getClientConfig(String clientId) {
        CommonConstants.LOGGER.info("Get Client enrichment config : {}", clientId);
        BasicResponse<SubmissionClientConfigDTO> response = accessManagementConnector.getSubmissionClientConfig(clientId);
        if (HttpStatus.OK.value() == response.getStatus()) {
            return response.getResult();
        } else {
            CommonConstants.LOGGER.error("Error in getting client enrichment config : {} due to : {}", clientId, response.getError());
            throw new GraphQLException(String.format("Error fetching client enrichment config %s",clientId));
        }
    }

    public SubmissionAssigneeEntity addOrUpdateSubmissionAssignee(SubmissionAssigneeEntity submissionAssignee, DataFetchingEnvironment dataFetchingEnvironment) {
        String clientId = commonUtil.getClientId(dataFetchingEnvironment);
        String userId = commonUtil.getLoggedInUser(dataFetchingEnvironment);
        CommonConstants.LOGGER.info("Adding assignee to client : {}", clientId);
        this.validateUser(userId, dataFetchingEnvironment);
        BasicResponse<SubmissionAssigneeEntity> response = workflowManagerConnector.addOrUpdateSubmissionAssignee(submissionAssignee);
        if (HttpStatus.OK.value() == response.getStatus()) {
            return response.getResult();
        } else {
            CommonConstants.LOGGER.error("Error adding assignee to client : {} due to : {}", clientId, response.getError());
            throw new GraphQLException(String.format("Error adding assignee to client %s",clientId));
        }
    }

    public SubmissionAssigneeEntity getClientAssignee(DataFetchingEnvironment environment) {
        String clientId = commonUtil.getClientId(environment);
        CommonConstants.LOGGER.info("Get Client enrichment config : {}", clientId);
        BasicResponse<SubmissionAssigneeEntity> response = workflowManagerConnector.findSubmissionAssignee(clientId);
        if (HttpStatus.OK.value() == response.getStatus()) {
            return response.getResult();
        } else {
            CommonConstants.LOGGER.error("Error in getting client assignee : {} due to : {}", clientId, response.getError());
            throw new GraphQLException(String.format("Error fetching client assignee %s",clientId));
        }
    }

    public void validateUser(String userId, DataFetchingEnvironment dataFetchingEnvironment) {
        if(!StringUtils.isEmpty(userId)) {
            BasicResponse<UserInfoResponse> response = accessManagementConnector.getUserById(userId, commonUtil.getClientId(dataFetchingEnvironment),commonUtil.getToken(dataFetchingEnvironment));
            if (HttpStatus.NOT_FOUND.value() == response.getStatus()) {
                throw new GraphQLException("User doesn't exist.");
            }
            else if (HttpStatus.OK.value() != response.getStatus()){
                throw new GraphQLException("Error while validating user from Quest due to : "+ response.getError());
            }
        }
    }
}
