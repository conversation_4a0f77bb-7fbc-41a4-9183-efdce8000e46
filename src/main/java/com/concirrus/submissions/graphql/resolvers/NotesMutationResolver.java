package com.concirrus.submissions.graphql.resolvers;

import com.concirrus.quest.common.rest.model.BasicResponse;
import com.concirrus.submission.connector.accessmanagement.AccessManagementConnector;
import com.concirrus.submission.connector.accessmanagement.model.UserInfoResponse;
import com.concirrus.submission.connector.submissionmanager.SubmissionManagerConnector;
import com.concirrus.submission.service.notes.connector.notes.NotesServiceConnector;
import com.concirrus.submission.service.notes.connector.notes.model.NoteCreationRequest;
import com.concirrus.submission.service.notes.connector.notes.model.NoteUpdateRequest;
import com.concirrus.submissions.common.CommonConstants;
import com.concirrus.submissions.common.CommonUtil;
import com.concirrus.submissions.graphql.model.EntityType;
import com.concirrus.submissions.graphql.model.Note;
import com.concirrus.submissions.graphql.model.NotesMutations;
import com.concirrus.submissions.graphql.model.Type;
import com.concirrus.submissions.integration.AccessManagementSal;
import com.concirrus.submissions.model.User;
import com.concirrus.submissions.util.SanitizeHTML;
import graphql.GraphQLException;
import graphql.kickstart.tools.GraphQLResolver;
import graphql.schema.DataFetchingEnvironment;
import io.github.resilience4j.ratelimiter.annotation.RateLimiter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Objects;

@Component
public class NotesMutationResolver implements GraphQLResolver<NotesMutations> {

    @Autowired
    NotesServiceConnector notesServiceConnector;

    @Autowired
    SubmissionManagerConnector submissionManagerConnector;

    @Autowired
    AccessManagementSal accessManagementSal;

    @Autowired
    AccessManagementConnector accessManagementConnector;

    @Autowired
    CommonUtil commonUtil;

    private static final Logger logger = LoggerFactory.getLogger(NotesMutationResolver.class);

    @RateLimiter(name = "notes_limiter")
    public Note createNote(NotesMutations notesMutations, String entityId, EntityType entityType, String noteText, Type type, String subEntityType, String subEntityId, String noteType, DataFetchingEnvironment dataFetchingEnvironment) {
        String clientId = commonUtil.getClientId(dataFetchingEnvironment);
        boolean dashboardEnabled = commonUtil.isFeatureEnabled(clientId, CommonConstants.SUBMISSION_DASHBOARD_FEATURE);
        if (!dashboardEnabled) {
            throw new GraphQLException("Dashboard is disabled");
        }
        NoteCreationRequest noteCreationRequest = new NoteCreationRequest();

        if(org.apache.commons.lang3.StringUtils.isNotBlank(noteType)){
            noteCreationRequest.setNoteType(noteType);
        }else{
            noteCreationRequest.setNoteType(com.concirrus.submission.service.notes.connector.notes.model.NoteType.SUBMISSION.name());
        }

        noteCreationRequest.setSubmissionId(entityId);
        noteCreationRequest.setType(type.name());
        noteCreationRequest.setSubEntityType(subEntityType);
        noteCreationRequest.setSubEntityId(subEntityId);

        // HTML-encode the noteText to prevent XSS attacks
        noteText = SanitizeHTML.sanitizeValue(noteText);

        noteCreationRequest.setText(noteText);
        String userId = commonUtil.getLoggedInUser(dataFetchingEnvironment);
        com.concirrus.submission.service.notes.connector.notes.model.Note note =  notesServiceConnector.createNoteByEntityType(noteCreationRequest,userId,clientId, entityId, entityType.name()).getResult();
        User user = null;
        if(!StringUtils.isEmpty(note.getUserId()))
        {
            user= getUserById(note.getUserId(), clientId,commonUtil.getToken(dataFetchingEnvironment));
        }
        return new Note(note.getId(),user,note.getTime().toString(), note.getText(), note.getNoteType(), note.getSubEntityId(), note.getSubEntityType());
    }

    public User getUserById(String userId,String clientId, String token) {
        CommonConstants.LOGGER.info("Get all users for clientId : {}", clientId);
        BasicResponse<UserInfoResponse> userInfoResponse = accessManagementConnector.getUserById(userId,clientId,token);
        if (Objects.nonNull(userInfoResponse) && HttpStatus.OK.value() == userInfoResponse.getStatus()&& userInfoResponse.getResult()!=null) {
          return User.getInstance(userInfoResponse.getResult());
        }
       else
        {
            logger.error("Error while fetching user from User service for userId: {} due to : {}",userId,userInfoResponse.getError());
            return null;
        }
    }

    public boolean deleteNote(NotesMutations notesMutations,String noteId,DataFetchingEnvironment dataFetchingEnvironment) {
        try {
            String clientId = commonUtil.getClientId(dataFetchingEnvironment);
            boolean dashboardEnabled = commonUtil.isFeatureEnabled(clientId, CommonConstants.SUBMISSION_DASHBOARD_FEATURE);
            if (!dashboardEnabled) {
                throw new GraphQLException("Dashboard is disabled");
            }
            BasicResponse<com.concirrus.submission.service.notes.connector.notes.model.Note> noteByIdResponse = notesServiceConnector.getNoteById(noteId, "",clientId);
            if (noteByIdResponse.getStatus() != 200) {
                throw new RuntimeException(String.format("Error while fetching notes from note service for id : {%s}",noteId));
            }
            String userId = commonUtil.getLoggedInUser(dataFetchingEnvironment);
            notesServiceConnector.deleteNote(noteId,userId,clientId);
            return true;
        }catch (Exception ex){
            logger.error("Error while deleting note due to : {}",ex.getMessage());
            return false;
        }
    }

    public Note updateNote(NotesMutations notesMutations, String noteId, String noteText, DataFetchingEnvironment dataFetchingEnvironment) {
        String clientId = commonUtil.getClientId(dataFetchingEnvironment);
        boolean dashboardEnabled = commonUtil.isFeatureEnabled(clientId, CommonConstants.SUBMISSION_DASHBOARD_FEATURE);
        if (!dashboardEnabled) {
            throw new GraphQLException("Dashboard is disabled");
        }
        BasicResponse<com.concirrus.submission.service.notes.connector.notes.model.Note> noteByIdResponse = notesServiceConnector.getNoteById(noteId, "",clientId);
        if (noteByIdResponse.getStatus() != 200) {
            throw new RuntimeException(String.format("Error while fetching notes from note service for id : {%s}",noteId));
        }
        NoteUpdateRequest noteUpdateRequest = new NoteUpdateRequest();

        // HTML-encode the noteText to prevent XSS attacks
        noteText = SanitizeHTML.sanitizeValue(noteText);

        noteUpdateRequest.setText(noteText);
        String userId = commonUtil.getLoggedInUser(dataFetchingEnvironment);
        com.concirrus.submission.service.notes.connector.notes.model.Note note =  notesServiceConnector.updateNote(noteId,noteUpdateRequest,userId,clientId).getResult();
        User user = null;
        User updatedByUser = null;
        if(!StringUtils.isEmpty(note.getUserId()))
        {
            user= getUserById(note.getUserId(), clientId,commonUtil.getToken(dataFetchingEnvironment));
        }

        if(!StringUtils.isEmpty(note.getEditorUserId()))
        {
            updatedByUser = getUserById(note.getEditorUserId(), clientId,commonUtil.getToken(dataFetchingEnvironment));
        }
        return new Note(note.getId(),user,note.getTime().toString(), note.getText(), note.getNoteType(), note.getSubEntityId(), note.getSubEntityType(), note.getEditTime(), note.getEditorUserId(), updatedByUser);
    }

}
