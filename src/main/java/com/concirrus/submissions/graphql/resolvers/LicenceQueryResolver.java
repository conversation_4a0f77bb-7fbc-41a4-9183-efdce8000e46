package com.concirrus.submissions.graphql.resolvers;

import com.concirrus.submissions.common.CommonUtil;
import com.concirrus.submissions.graphql.model.LicenceDetail;
import com.concirrus.submissions.graphql.model.LicenceQuery;
import com.concirrus.submissions.model.repository.LicenceHash;
import com.concirrus.submissions.repository.LicenceHashRepository;
import graphql.GraphQLException;
import graphql.kickstart.tools.GraphQLResolver;
import graphql.schema.DataFetchingEnvironment;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class LicenceQueryResolver implements GraphQLResolver<LicenceQuery> {
    private final LicenceHashRepository licenceHashRepository;

    private final CommonUtil commonUtil;

    public LicenceQueryResolver(LicenceHashRepository licenceHashRepository , CommonUtil commonUtil) {
        this.licenceHashRepository = licenceHashRepository;
        this.commonUtil = commonUtil;
    }

    public LicenceDetail licenceDetail(LicenceQuery licenceQuery, DataFetchingEnvironment environment) {
        String clientId = commonUtil.getClientId(environment);
        Optional<LicenceHash> licence = licenceHashRepository.findById(clientId);
        if (!licence.isPresent()) {
            throw new GraphQLException("No licence found for the client");
        }
        return LicenceDetail.from(licence.get());
    }
}
