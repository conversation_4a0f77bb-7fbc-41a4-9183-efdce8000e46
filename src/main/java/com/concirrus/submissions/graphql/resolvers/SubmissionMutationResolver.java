package com.concirrus.submissions.graphql.resolvers;

import com.concirrus.quest.common.rest.model.BasicResponse;
import com.concirrus.submission.common.validator.ContentValidator;
import com.concirrus.submission.connector.accessmanagement.model.UserInfoResponse;
import com.concirrus.submission.connector.policyintegration.PolicyIntegrationConnector;
import com.concirrus.submission.connector.policyintegration.model.PolicySummary;
import com.concirrus.submission.connector.submissionmanager.SubmissionManagerConnector;
import com.concirrus.submission.connector.submissionmanager.model.AssignUserRequest;
import com.concirrus.submission.connector.submissionmanager.model.Submission;
import com.concirrus.submission.connector.submissionmanager.model.SubmissionDetailResponse;
import com.concirrus.submission.connector.submissionmanager.model.SubmissionState;
import com.concirrus.submissions.common.CommonConstants;
import com.concirrus.submissions.common.CommonUtil;
import com.concirrus.submissions.connector.questinsights.QuestInsightsConnector;
import com.concirrus.submissions.connector.questinsights.model.account.Account;
import com.concirrus.submissions.connector.questinsights.model.referencedata.Product;
import com.concirrus.submissions.graphql.model.*;
import com.concirrus.submissions.graphql.security.Sentry;
import com.concirrus.submissions.integration.AccessManagementSal;
import com.concirrus.submissions.model.SubmissionStateStatusChangeRequest;
import com.concirrus.submissions.service.mapper.SubmissionMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import graphql.GraphQLException;
import graphql.kickstart.tools.GraphQLResolver;
import graphql.schema.DataFetchingEnvironment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class SubmissionMutationResolver implements GraphQLResolver<SubmissionMutation> {

    @Autowired
    QuestInsightsConnector questInsightsConnector;

    @Autowired
    SubmissionManagerConnector submissionManagerConnector;

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    CommonUtil commonUtil;

    @Autowired
    SubmissionMapper submissionMapper;

    @Autowired
    AccessManagementSal accessManagementSal;

    Sentry sentry;

    private PolicyIntegrationConnector policyIntegrationConnector;

    public SubmissionMutationResolver(QuestInsightsConnector questInsightsConnector, SubmissionManagerConnector submissionManagerConnector, ObjectMapper objectMapper, CommonUtil commonUtil,SubmissionMapper submissionMapper, PolicyIntegrationConnector policyIntegrationConnector,Sentry sentry,AccessManagementSal accessManagementSal) {
        this.questInsightsConnector = questInsightsConnector;
        this.submissionManagerConnector = submissionManagerConnector;
        this.objectMapper = objectMapper;
        this.commonUtil = commonUtil;
        this.submissionMapper =submissionMapper;
        this.policyIntegrationConnector = policyIntegrationConnector;
        this.sentry = sentry;
        this.accessManagementSal = accessManagementSal;
    }

    public String create(SubmissionMutation submissionMutation, com.concirrus.submissions.model.SubmissionCreationRequest submissionCreationRequest, DataFetchingEnvironment environment) {
        CommonConstants.LOGGER.info(String.format("Create submission %s", submissionCreationRequest.toString()));
        ContentValidator.validate(submissionCreationRequest);
        boolean dashboardEnabled = commonUtil.isFeatureEnabled(commonUtil.getClientId(environment), CommonConstants.SUBMISSION_DASHBOARD_FEATURE);
        if (!dashboardEnabled) {
            throw new GraphQLException("Dashboard is disabled");
        }
        com.concirrus.submission.connector.submissionmanager.model.SubmissionRequest submissionCreation = submissionMapper.getInstance(commonUtil.getClientId(environment), submissionCreationRequest);
        BasicResponse<String> response = submissionManagerConnector.createSubmission(commonUtil.getClientId(environment), submissionCreation);
        if (HttpStatus.CREATED.value() == response.getStatus()) {
            CommonConstants.LOGGER.info("Submission Created Successfully {}", response.getResult());
            return response.getResult();
        } else {
            CommonConstants.LOGGER.error("Exception while creating submission : {} ", response.getError());
            throw new RuntimeException("Exception while creating submission.");
        }
    }

    public boolean editSubmission(SubmissionMutation submissionMutation, String submissionId, SubmissionEditRequest editSubmissionRequest, DataFetchingEnvironment environment) {
        CommonConstants.LOGGER.info("Edit submission: {}", editSubmissionRequest);
        List<VesselInfoInput> vessels = editSubmissionRequest.getVessels();
        if (!CollectionUtils.isEmpty(vessels)) {
            for (VesselInfoInput vessel : vessels) {
                ContentValidator.validate(vessel);
            }
        }
        List<NodeInput> policyInfo = editSubmissionRequest.getPolicyInfo();
        if (!CollectionUtils.isEmpty(policyInfo)) {
            for (NodeInput node : policyInfo) {
                ContentValidator.validate(node);
            }
        }
        ContentValidator.validate(editSubmissionRequest);
        boolean dashboardEnabled = commonUtil.isFeatureEnabled(commonUtil.getClientId(environment), CommonConstants.SUBMISSION_DASHBOARD_FEATURE);
        if (!dashboardEnabled) {
            throw new GraphQLException("Dashboard is disabled");
        }
        BasicResponse<Submission> submissionByIdResponse = submissionManagerConnector.getSubmissionById(submissionId, commonUtil.getClientId(environment), Boolean.FALSE);
        if (submissionByIdResponse.getStatus() != 200) {
            throw new RuntimeException(submissionByIdResponse.getError().toString());
        }
        if (SubmissionState.INBOX.equals(submissionByIdResponse.getResult().getState())) {
            com.concirrus.submission.connector.submissionmanager.model.SubmissionRequest editSubmissionObject = submissionMapper.getInstance(editSubmissionRequest,commonUtil.getClientId(environment));
            BasicResponse<Submission> response = submissionManagerConnector.editSubmission(commonUtil.getClientId(environment), submissionId, editSubmissionObject);
            if (HttpStatus.OK.value() == response.getStatus()) {
                CommonConstants.LOGGER.info("Submission Edited Successfully.");
                return true;
            } else {
                CommonConstants.LOGGER.error("Exception while editing submission : {}");
                throw new GraphQLException(String.format("Exception while editing submission : %s", response.getError()));
            }
        } else {
            throw new GraphQLException(String.format("Bad Request : Can't edit submission in %s state", submissionByIdResponse.getResult().getState()));
        }
    }

    public boolean assignSubmission(SubmissionMutation submissionMutation, String submissionId, String userId, DataFetchingEnvironment dataFetchingEnvironment) {
        CommonConstants.LOGGER.info("Assign user id {} to submission id {}",userId,submissionId);
        boolean dashboardEnabled = commonUtil.isFeatureEnabled(commonUtil.getClientId(dataFetchingEnvironment), CommonConstants.SUBMISSION_DASHBOARD_FEATURE);
        if (!dashboardEnabled) {
            throw new GraphQLException("Dashboard is disabled");
        }
        validateUser(userId, dataFetchingEnvironment);
        AssignUserRequest assignUserRequest = new AssignUserRequest();
        assignUserRequest.setUserId(userId);
        BasicResponse<Boolean> response = submissionManagerConnector.assignUserToSubmission(commonUtil.getClientId(dataFetchingEnvironment), submissionId, assignUserRequest);
        if (HttpStatus.OK.value() == response.getStatus()) {
            CommonConstants.LOGGER.info("User {} is assigned to Submission : {}",userId,submissionId);
            return response.getResult();
        } else {
            CommonConstants.LOGGER.error("User {} is not assigned to Submission : {} due to {}",userId,submissionId,response.getError());
            throw new GraphQLException((String) response.getError());
        }
    }

    public boolean validateUser(String userId, DataFetchingEnvironment dataFetchingEnvironment) {
        if(StringUtils.hasText(userId)) {
            BasicResponse<UserInfoResponse> response = accessManagementSal.getUserById(userId,commonUtil.getClientId(dataFetchingEnvironment), commonUtil.getToken(dataFetchingEnvironment));
            if (HttpStatus.NOT_FOUND.value() == response.getStatus()) {
                throw new GraphQLException("User doesn't exist.");
            }
            else if (HttpStatus.OK.value() != response.getStatus()){
                throw new GraphQLException("Error while validating user from Quest due to : "+ response.getError());
            }
        }
        return true;
    }

    public boolean editSubmissionStateStatus(SubmissionMutation submissionMutation, SubmissionStateStatusChangeRequest submissionStateStatusChangeRequest, String submissionId, DataFetchingEnvironment dataFetchingEnvironment) {
        CommonConstants.LOGGER.info("Change state of submission {}",submissionId);
        ContentValidator.validate(submissionStateStatusChangeRequest);
        boolean dashboardEnabled = commonUtil.isFeatureEnabled(commonUtil.getClientId(dataFetchingEnvironment), CommonConstants.SUBMISSION_DASHBOARD_FEATURE);
        if (!dashboardEnabled) {
            throw new GraphQLException("Dashboard is disabled");
        }
        com.concirrus.submission.connector.submissionmanager.model.SubmissionStateStatusChangeRequest request = objectMapper.convertValue(submissionStateStatusChangeRequest, com.concirrus.submission.connector.submissionmanager.model.SubmissionStateStatusChangeRequest.class);
        BasicResponse<Boolean> response = submissionManagerConnector.changeSubmissionStateStatus(commonUtil.getClientId(dataFetchingEnvironment),commonUtil.getToken(dataFetchingEnvironment), submissionId, request);
        if (HttpStatus.OK.value() == response.getStatus()) {
            CommonConstants.LOGGER.info("Change status and state of submission {} successful .",submissionId);
            return response.getResult();
        } else {
            CommonConstants.LOGGER.error("Error while changing state and state of submission {} due to {} .",submissionId,response.getError().toString());
            throw new GraphQLException((String) response.getError());
        }
    }

    public Boolean deleteSubmission(SubmissionMutation submissionMutation, DeleteSubmissionRequest deleteSubmissionRequest, DataFetchingEnvironment dataFetchingEnvironment) {
        String clientId = commonUtil.getClientId(dataFetchingEnvironment);
        boolean dashboardEnabled = commonUtil.isFeatureEnabled(clientId, CommonConstants.SUBMISSION_DASHBOARD_FEATURE);
        if (!dashboardEnabled) {
            throw new GraphQLException("Dashboard is disabled");
        }
        String userId = commonUtil.getLoggedInUser(dataFetchingEnvironment);
        ContentValidator.validate(deleteSubmissionRequest);
        BasicResponse<List<SubmissionDetailResponse>> submissionBasicResponse = submissionManagerConnector.getSubmissionsByIds(Arrays.asList(deleteSubmissionRequest.getSubmissionId()), clientId, Boolean.FALSE);
        if (HttpStatus.OK.value() == submissionBasicResponse.getStatus() && !CollectionUtils.isEmpty(submissionBasicResponse.getResult())) {
            final String token = commonUtil.getToken(dataFetchingEnvironment);
            SubmissionDetailResponse submissionDetailResponse = submissionBasicResponse.getResult().get(0);
            //Only delete submission in Inbox,InReview and Quoted state
            if (SubmissionState.INBOX.equals(submissionDetailResponse.getState()) || SubmissionState.IN_REVIEW.equals(submissionDetailResponse.getState()) || SubmissionState.QUOTED.equals(submissionDetailResponse.getState())) {
                //Delete Policy only in review and quoted state
                if (SubmissionState.IN_REVIEW.equals(submissionDetailResponse.getState()) || SubmissionState.QUOTED.equals(submissionDetailResponse.getState())) {
                    //Validate User Deleting Submission.
                    validateUser(userId, dataFetchingEnvironment);
                    BasicResponse<Boolean> deletePolicyResponse = policyIntegrationConnector.deletePolicy(clientId, token, submissionDetailResponse.getReferenceId());
                    if (HttpStatus.OK.value() == deletePolicyResponse.getStatus() && deletePolicyResponse.getResult()) {
                        //Delete Associated account
                        if (deleteSubmissionRequest.getDeleteAssociatedAccount()) {
                            deleteAccount(clientId, token, submissionDetailResponse);
                        }
                    } else {
                        throw new GraphQLException(String.format("Error while delete policy for submission id : %s", deletePolicyResponse.getError()));
                    }
                }
                //Delete Submission and related entities like notes, content etc.
                return deleteSubmissionEntity(deleteSubmissionRequest, clientId, userId);
            } else {
                throw new GraphQLException(String.format("Can't delete submission : %s in %s state .", deleteSubmissionRequest.getSubmissionId(), submissionDetailResponse.getState()));
            }
        } else {
            throw new GraphQLException(String.format("Submission with submission id : %s doesn't exist.", deleteSubmissionRequest.getSubmissionId()));
        }
    }

    private void deleteAccount(String clientId, String token, SubmissionDetailResponse submissionDetailResponse) {
        Map<String, Object> policyInfo = com.concirrus.submissions.graphql.model.Submission.getPolicyInfoMap(submissionDetailResponse, objectMapper);
        if (policyInfo!=null && policyInfo.containsKey("counterPartyId")) {
            String accountId = policyInfo.get("counterPartyId").toString();
            BasicResponse<List<Product>> productListResponse = questInsightsConnector.getProducts(submissionDetailResponse.getProductLine(), token, clientId);
            String productList = submissionDetailResponse.getProductName();
            if(HttpStatus.OK.value() == productListResponse.getStatus() && CollectionUtils.isEmpty(productListResponse.getResult())){
                List<String> productName1List = productListResponse.getResult().stream().map(Product::getName).collect(Collectors.toList());
                productList = String.join(",", productName1List);
            }
            BasicResponse<List<PolicySummary>> policySummaryResponse = policyIntegrationConnector.getPoliciesByAccountIdAndProductAndProductLine(clientId, token, accountId, productList, submissionDetailResponse.getProductLine());
            if (HttpStatus.OK.value() == policySummaryResponse.getStatus() && CollectionUtils.isEmpty(policySummaryResponse.getResult())) {
                CommonConstants.LOGGER.info("Going to delete account ", accountId);
                BasicResponse<Boolean> deleteAccountResponse = questInsightsConnector.deleteAccountByAccountId(accountId, token, clientId);
                if (HttpStatus.OK.value() != deleteAccountResponse.getStatus() || !deleteAccountResponse.getResult()) {
                    String deleteAccountError = String.format("Error while deleting associated account : {%s} due to {%s}", accountId, deleteAccountResponse.getError());
                    CommonConstants.LOGGER.error(deleteAccountError);
                    throw new GraphQLException(deleteAccountError);
                }
            } else {
                CommonConstants.LOGGER.error("More policies associated with account id : {} . Account can't be deleted.", accountId);
            }
        }
        //TODO : TO be removed, added till UI doesn't save accountId.
        else
        {
            if(submissionDetailResponse.getAccountName()!=null) {
                BasicResponse<Account> accountBasicResponse =  questInsightsConnector.getAccountByName(submissionDetailResponse.getAccountName().getValue(), token, clientId);
                if(accountBasicResponse!=null && HttpStatus.OK.value() ==accountBasicResponse.getStatus()&& accountBasicResponse.getResult()!=null)
                {
                    final String accountId = accountBasicResponse.getResult().getId();
                    BasicResponse<List<Product>> productListResponse = questInsightsConnector.getProducts(submissionDetailResponse.getProductLine(), token, clientId);
                    String productList = submissionDetailResponse.getProductName();
                    if(HttpStatus.OK.value() == productListResponse.getStatus() && CollectionUtils.isEmpty(productListResponse.getResult())){
                        List<String> productName1List = productListResponse.getResult().stream().map(Product::getName).collect(Collectors.toList());
                        productList = String.join(",", productName1List);
                    }
                    BasicResponse<List<PolicySummary>> policySummaryResponse = policyIntegrationConnector.getPoliciesByAccountIdAndProductAndProductLine(clientId, token, accountId, productList, submissionDetailResponse.getProductLine());
                    if (HttpStatus.OK.value() == policySummaryResponse.getStatus() && CollectionUtils.isEmpty(policySummaryResponse.getResult())) {
                        CommonConstants.LOGGER.info("Going to delete account ", accountId);
                        BasicResponse<Boolean> deleteAccountResponse = questInsightsConnector.deleteAccountByAccountId(accountId, token, clientId);
                        if (HttpStatus.OK.value() != deleteAccountResponse.getStatus() || !deleteAccountResponse.getResult()) {
                            String deleteAccountError = String.format("Error while deleting associated account : {%s} due to {%s}", accountId, deleteAccountResponse.getError());
                            CommonConstants.LOGGER.error(deleteAccountError);
                            throw new GraphQLException(deleteAccountError);
                        }
                    } else {
                        CommonConstants.LOGGER.error("More policies associated with account id : {} . Account can't be deleted.", accountId);
                    }
                }
            }
        }
    }

    private Boolean deleteSubmissionEntity(DeleteSubmissionRequest deleteSubmissionRequest, String clientId, String userId) {
        com.concirrus.submission.connector.submissionmanager.model.DeleteSubmissionRequest deleteSubmission = new com.concirrus.submission.connector.submissionmanager.model.DeleteSubmissionRequest();
        deleteSubmission.setUserId(userId);
        deleteSubmission.setReason(deleteSubmissionRequest.getDeletionReason());
        BasicResponse<Boolean> deleteSubmissionResponse = submissionManagerConnector.deleteSubmission(clientId, deleteSubmission, deleteSubmissionRequest.getSubmissionId());
        if(HttpStatus.OK.value() == deleteSubmissionResponse.getStatus() && Boolean.TRUE.equals(deleteSubmissionResponse.getResult()))
        {
            return true;
        }
        else
        {
            CommonConstants.LOGGER.error("Error while deleting submission from submission manager service due to : {}", deleteSubmissionResponse.getError());
            throw new GraphQLException(String.format("Error while deleting submission %s .", deleteSubmissionRequest.getSubmissionId()));
        }
    }


}
