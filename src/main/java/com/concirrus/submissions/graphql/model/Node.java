package com.concirrus.submissions.graphql.model;

import com.concirrus.submission.common.validator.Content;
import com.concirrus.submission.common.validator.ContentTypeEnum;
import com.concirrus.submissions.common.CommonConstants;

public class Node {
    // added Content validations as per VAPT-471
    @Content(type = ContentTypeEnum.NO_HTML, maxLength = CommonConstants.MAX_CONTENT_LENGTH_100)
    private String key;
    @Content(type = ContentTypeEnum.NO_HTML, maxLength = CommonConstants.MAX_CONTENT_LENGTH_100)
    private String value;

    public Node() {
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
