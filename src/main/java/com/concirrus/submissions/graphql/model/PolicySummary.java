package com.concirrus.submissions.graphql.model;

public class PolicySummary {
    private String id;
    private String policyNumber;
    private String accountId;
    private String policyType;
    private String policyStatus;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPolicyNumber() {
        return policyNumber;
    }

    public void setPolicyNumber(String policyNumber) {
        this.policyNumber = policyNumber;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getPolicyType() {
        return policyType;
    }

    public void setPolicyType(String policyType) {
        this.policyType = policyType;
    }

    public String getPolicyStatus() {
        return policyStatus;
    }

    public void setPolicyStatus(String policyStatus) {
        this.policyStatus = policyStatus;
    }

    public static PolicySummary from (com.concirrus.submission.connector.policyintegration.model.PolicySummary policySummary)
    {
        PolicySummary policySummaryObj = new PolicySummary();
        policySummaryObj.setAccountId(policySummary.getAccountId());
        policySummaryObj.setId(policySummary.getInsurancePolicyId());
        policySummaryObj.setPolicyNumber(policySummary.getPolicyNumber());
        policySummaryObj.setPolicyType(policySummary.getPolicyType() == null ? "" : policySummary.getPolicyType().toString());
        policySummaryObj.setPolicyStatus(policySummary.getPolicyStatus() == null ? "" :policySummary.getPolicyStatus().toString());
        return policySummaryObj;
    }
}
