package com.concirrus.submissions.graphql.model;

import com.fasterxml.jackson.databind.ObjectMapper;
import graphql.language.StringValue;
import graphql.schema.Coercing;
import graphql.schema.CoercingParseLiteralException;
import graphql.schema.GraphQLScalarType;

import java.util.Map;

public class JsonScalarType extends GraphQLScalarType {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public JsonScalarType() {
        super("JSON", "Arbitrary JSON value", new Coercing<Object, Object>() {
            @Override
            public Object serialize(Object dataFetcherResult) {
                return dataFetcherResult;
            }

            @Override
            public Object parseValue(Object input) {
                return input;
            }

            @Override
            public Object parseLiteral(Object input) {
                if (input instanceof StringValue) {
                    try {
                        return objectMapper.readValue(((StringValue) input).getValue(), Map.class);
                    } catch (Exception e) {
                        throw new CoercingParseLiteralException("Invalid JSON value");
                    }
                }
                throw new CoercingParseLiteralException("Expected a JSON string");
            }
        });
    }
}
