package com.concirrus.submissions.graphql.model;

import com.concirrus.submissions.model.repository.LicenceHash;

import java.time.LocalDate;

public class LicenceDetail {
    private String clientId;
    private String licenceType;
    private LocalDate validFrom;
    private LocalDate validUntil;

    public LicenceDetail() {
        //No Argument Constructor
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getLicenceType() {
        return licenceType;
    }

    public void setLicenceType(String licenceType) {
        this.licenceType = licenceType;
    }

    public LocalDate getValidFrom() {
        return validFrom;
    }

    public void setValidFrom(LocalDate validFrom) {
        this.validFrom = validFrom;
    }

    public LocalDate getValidUntil() {
        return validUntil;
    }

    public void setValidUntil(LocalDate validUntil) {
        this.validUntil = validUntil;
    }

    public static LicenceDetail from(LicenceHash licenceHash) {
        LicenceDetail licenceDetail = new LicenceDetail();
        licenceDetail.setClientId(licenceHash.getClientId());
        licenceDetail.setLicenceType(licenceHash.getLicenceType().name());
        licenceDetail.setValidFrom(licenceHash.getValidFrom());
        licenceDetail.setValidUntil(licenceHash.getValidUntil());
        return licenceDetail;
    }
}
