package com.concirrus.submissions.graphql.model;

import com.concirrus.submission.common.validator.Content;
import com.concirrus.submission.common.validator.ContentTypeEnum;
import com.concirrus.submissions.common.CommonConstants;

import javax.validation.constraints.Size;

public class VesselInfoInput {
    // added Content validations as per VAPT-471
    @Content(type = ContentTypeEnum.NO_HTML, maxLength = CommonConstants.MAX_CONTENT_LENGTH_100)
    private String vesselId;
    @Content(type = ContentTypeEnum.NO_HTML, maxLength = CommonConstants.MAX_CONTENT_LENGTH_100)
    private String imo;
    @Size(max = CommonConstants.MAX_CONTENT_LENGTH_100)
    private String name;
    @Content(type = ContentTypeEnum.NO_HTML, maxLength = CommonConstants.MAX_CONTENT_LENGTH_100)
    private String productType;
    @Content(type = ContentTypeEnum.NO_HTML, maxLength = CommonConstants.MAX_CONTENT_LENGTH_100)
    private String coverFromDate;
    @Content(type = ContentTypeEnum.NO_HTML, maxLength = CommonConstants.MAX_CONTENT_LENGTH_100)
    private String coverToDate;
    private Double deadWeightTonnage;
    private Double grossTonnage;
    private Integer yearOfBuild;
    private Double premium;
    private Double deductible;
    private Double sumInsured;
    private Double increasedValue;
    @Content(type = ContentTypeEnum.NO_HTML, maxLength = CommonConstants.MAX_CONTENT_LENGTH_3)
    private String premiumCurrencyCode;
    @Content(type = ContentTypeEnum.NO_HTML, maxLength = CommonConstants.MAX_CONTENT_LENGTH_3)
    private String sumInsuredCurrencyCode;
    @Content(type = ContentTypeEnum.NO_HTML, maxLength = CommonConstants.MAX_CONTENT_LENGTH_3)
    private String deductibleCurrencyCode;
    private String mmsi;
    private String flag;
    private String callSign;
    private String vesselClass;
    private String breadth;
    private String Width;
    private Object additionalAttributes;
    private Boolean nonImo;
    private String length;

    public VesselInfoInput() {
    }

    public String getCallSign() {
        return callSign;
    }

    public void setCallSign(String callSign) {
        this.callSign = callSign;
    }

    public String getMmsi() {

        return mmsi;
    }

    public void setMmsi(String mmsi) {
        this.mmsi = mmsi;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public String getVesselClass() {
        return vesselClass;
    }

    public void setVesselClass(String vesselClass) {
        this.vesselClass = vesselClass;
    }

    public String getBreadth() {
        return breadth;
    }

    public void setBreadth(String breadth) {
        this.breadth = breadth;
    }

    public String getWidth() {
        return Width;
    }

    public void setWidth(String width) {
        Width = width;
    }

    public Object getAdditionalAttributes() {
        return additionalAttributes;
    }

    public void setAdditionalAttributes(Object additionalAttributes) {
        this.additionalAttributes = additionalAttributes;
    }

    public Boolean getNonImo() {
        return nonImo;
    }

    public void setNonImo(Boolean nonImo) {
        this.nonImo = nonImo;
    }

    public String getVesselId() {
        return vesselId;
    }

    public void setVesselId(String vesselId) {
        this.vesselId = vesselId;
    }

    public String getImo() {
        return imo;
    }

    public void setImo(String imo) {
        this.imo = imo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getCoverFromDate() {
        return coverFromDate;
    }

    public void setCoverFromDate(String coverFromDate) {
        this.coverFromDate = coverFromDate;
    }

    public String getCoverToDate() {
        return coverToDate;
    }

    public void setCoverToDate(String coverToDate) {
        this.coverToDate = coverToDate;
    }

    public Double getDeadWeightTonnage() {
        return deadWeightTonnage;
    }

    public void setDeadWeightTonnage(Double deadWeightTonnage) {
        this.deadWeightTonnage = deadWeightTonnage;
    }

    public Double getGrossTonnage() {
        return grossTonnage;
    }

    public void setGrossTonnage(Double grossTonnage) {
        this.grossTonnage = grossTonnage;
    }

    public Integer getYearOfBuild() {
        return yearOfBuild;
    }

    public void setYearOfBuild(Integer yearOfBuild) {
        this.yearOfBuild = yearOfBuild;
    }

    public Double getPremium() {
        return premium;
    }

    public void setPremium(Double premium) {
        this.premium = premium;
    }

    public Double getDeductible() {
        return deductible;
    }

    public void setDeductible(Double deductible) {
        this.deductible = deductible;
    }

    public Double getSumInsured() {
        return sumInsured;
    }

    public void setSumInsured(Double sumInsured) {
        this.sumInsured = sumInsured;
    }

    public Double getIncreasedValue() {
        return increasedValue;
    }

    public void setIncreasedValue(Double increasedValue) {
        this.increasedValue = increasedValue;
    }

    public String getPremiumCurrencyCode() {
        return premiumCurrencyCode;
    }

    public void setPremiumCurrencyCode(String premiumCurrencyCode) {
        this.premiumCurrencyCode = premiumCurrencyCode;
    }

    public String getSumInsuredCurrencyCode() {
        return sumInsuredCurrencyCode;
    }

    public void setSumInsuredCurrencyCode(String sumInsuredCurrencyCode) {
        this.sumInsuredCurrencyCode = sumInsuredCurrencyCode;
    }

    public String getDeductibleCurrencyCode() {
        return deductibleCurrencyCode;
    }

    public String getLength() {
        return length;
    }

    public void setLength(String length) {
        this.length = length;
    }

    public void setDeductibleCurrencyCode(String deductibleCurrencyCode) {
        this.deductibleCurrencyCode = deductibleCurrencyCode;
    }
}
