package com.concirrus.submissions.graphql.model;

import com.concirrus.submission.common.validator.Content;
import com.concirrus.submission.common.validator.ContentTypeEnum;
import com.concirrus.submissions.common.CommonConstants;

public class DeleteSubmissionRequest {
    // added Content validations as per VAPT-471
    private String submissionId;
    private Boolean deleteAssociatedAccount;
    @Content(type = ContentTypeEnum.NO_HTML, maxLength = CommonConstants.MAX_CONTENT_LENGTH_100)
    private String deletionReason ;

    public String getSubmissionId() {
        return submissionId;
    }

    public void setSubmissionId(String submissionId) {
        this.submissionId = submissionId;
    }

    public Boolean getDeleteAssociatedAccount() {
        return deleteAssociatedAccount;
    }

    public void setDeleteAssociatedAccount(Boolean deleteAssociatedAccount) {
        this.deleteAssociatedAccount = deleteAssociatedAccount;
    }

    public String getDeletionReason() {
        return deletionReason;
    }

    public void setDeletionReason(String deletionReason) {
        this.deletionReason = deletionReason;
    }
}
