package com.concirrus.submissions.graphql.model;

import java.util.Date;

public class PolicyInfo {
    Date policyStartDate;
    Date policyEndDate;
    Double totalChargedPremium;
    Double brokeragePercentage;

    public Date getPolicyStartDate() {
        return policyStartDate;
    }

    public void setPolicyStartDate(Date policyStartDate) {
        this.policyStartDate = policyStartDate;
    }

    public Date getPolicyEndDate() {
        return policyEndDate;
    }

    public void setPolicyEndDate(Date policyEndDate) {
        this.policyEndDate = policyEndDate;
    }

    public Double getTotalChargedPremium() {
        return totalChargedPremium;
    }

    public void setTotalChargedPremium(Double totalChargedPremium) {
        this.totalChargedPremium = totalChargedPremium;
    }

    public Double getBrokeragePercentage() {
        return brokeragePercentage;
    }

    public void setBrokeragePercentage(Double brokeragePercentage) {
        this.brokeragePercentage = brokeragePercentage;
    }
}
