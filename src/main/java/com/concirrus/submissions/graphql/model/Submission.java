package com.concirrus.submissions.graphql.model;

import com.concirrus.submission.connector.submissionmanager.model.*;
import com.concirrus.submissions.model.User;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

public class Submission {
    private String submissionId;
    private SubmissionState submissionState;
    private SubmissionStatus status;
    private User assignedUser;
    private List<String> imos;
    private List<VesselInfo> vessels;
    private String productName;
    private Instant receivedDate;
    private Instant timeUpdated;
    private String productLine;
    private String comment;
    private String referenceId;
    private RiskInsight riskInsight;
    private String brokerName;
    private Double brokerNameProbability = 0.0;
    private String accountName;
    private Double accountNameProbability = 0.0;
    private String brokingHouse;
    private Double brokingHouseProbability = 0.0;
    private List<Node> policyInfo;
    private VesselScheduleStatus vesselScheduleStatus;
    private Integer unavailableRiskScoreVesselCount;
    private Integer fleetSize;
    private Integer unrecognizedVesselCount;
    private Integer nonImoCount;


    public Submission() {
        //No Argument Constructor
    }

    public Integer getUnrecognizedVesselCount() {
        return unrecognizedVesselCount;
    }

    public void setUnrecognizedVesselCount(Integer unrecognizedVesselCount) {
        this.unrecognizedVesselCount = unrecognizedVesselCount;
    }

    public Integer getNonImoCount() {
        return nonImoCount;
    }

    public void setNonImoCount(Integer nonImoCount) {
        this.nonImoCount = nonImoCount;
    }

    public String getSubmissionId() {
        return submissionId;
    }

    public void setSubmissionId(String submissionId) {
        this.submissionId = submissionId;
    }

    public SubmissionState getSubmissionState() {
        return submissionState;
    }

    public void setSubmissionState(SubmissionState submissionState) {
        this.submissionState = submissionState;
    }

    public SubmissionStatus getStatus() {
        return status;
    }

    public void setStatus(SubmissionStatus status) {
        this.status = status;
    }

    public User getAssignedUser() {
        return assignedUser;
    }

    public void setAssignedUser(User assignedUser) {
        this.assignedUser = assignedUser;
    }

    public List<String> getImos() {
        return imos;
    }

    public void setImos(List<String> imos) {
        this.imos = imos;
    }

    public List<VesselInfo> getVessels() {
        return vessels;
    }

    public void setVessels(List<VesselInfo> vessels) {
        this.vessels = vessels;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Instant getReceivedDate() {
        return receivedDate;
    }

    public void setReceivedDate(Instant receivedDate) {
        this.receivedDate = receivedDate;
    }

    public Instant getTimeUpdated() {
        return timeUpdated;
    }

    public void setTimeUpdated(Instant timeUpdated) {
        this.timeUpdated = timeUpdated;
    }

    public String getProductLine() {
        return productLine;
    }

    public void setProductLine(String productLine) {
        this.productLine = productLine;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(String referenceId) {
        this.referenceId = referenceId;
    }

    public RiskInsight getRiskInsight() {
        return riskInsight;
    }

    public void setRiskInsight(RiskInsight riskInsight) {
        this.riskInsight = riskInsight;
    }

    public String getBrokerName() {
        return brokerName;
    }

    public void setBrokerName(String brokerName) {
        this.brokerName = brokerName;
    }

    public Double getBrokerNameProbability() {
        return brokerNameProbability;
    }

    public void setBrokerNameProbability(Double brokerNameProbability) {
        this.brokerNameProbability = brokerNameProbability;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public Double getAccountNameProbability() {
        return accountNameProbability;
    }

    public void setAccountNameProbability(Double accountNameProbability) {
        this.accountNameProbability = accountNameProbability;
    }

    public String getBrokingHouse() {
        return brokingHouse;
    }

    public void setBrokingHouse(String brokingHouse) {
        this.brokingHouse = brokingHouse;
    }

    public Double getBrokingHouseProbability() {
        return brokingHouseProbability;
    }

    public void setBrokingHouseProbability(Double brokingHouseProbability) {
        this.brokingHouseProbability = brokingHouseProbability;
    }

    public List<Node> getPolicyInfo() {
        return policyInfo;
    }

    public void setPolicyInfo(List<Node> policyInfo) {
        this.policyInfo = policyInfo;
    }

    public VesselScheduleStatus getVesselScheduleStatus() {
        return vesselScheduleStatus;
    }

    public void setVesselScheduleStatus(VesselScheduleStatus vesselScheduleStatus) {
        this.vesselScheduleStatus = vesselScheduleStatus;
    }

    public Integer getUnavailableRiskScoreVesselCount() {
        return unavailableRiskScoreVesselCount;
    }

    public void setUnavailableRiskScoreVesselCount(Integer unavailableRiskScoreVesselCount) {
        this.unavailableRiskScoreVesselCount = unavailableRiskScoreVesselCount;
    }

    public Integer getFleetSize() {
        return fleetSize;
    }

    public void setFleetSize(Integer fleetSize) {
        this.fleetSize = fleetSize;
    }

    public static Submission from(SubmissionDetailResponse submissionDetailResponse, User user, ObjectMapper objectMapper) {
        Submission submission = new Submission();
        submission.setSubmissionId(submissionDetailResponse.getId());
        submission.setSubmissionState(submissionDetailResponse.getState());
        submission.setStatus(submissionDetailResponse.getStatus());
        submission.setAssignedUser(user);
        if (CollectionUtils.isEmpty(submissionDetailResponse.getVessels())) {
            submission.setImos(Collections.emptyList());
            submission.setVessels(Collections.emptyList());
        }  else {
            submission.setImos(
                    submissionDetailResponse.getVessels().stream()
                    .map(VesselInfo::getImo)
                    .collect(Collectors.toList())
            );
            submission.setVessels(submissionDetailResponse.getVessels());
        }
        submission.setProductName(submissionDetailResponse.getProductName());
        submission.setReceivedDate(submissionDetailResponse.getReceivedDate());
        submission.setTimeUpdated(submissionDetailResponse.getUpdatedAt());
        submission.setProductLine(submissionDetailResponse.getProductLine());
        submission.setComment(submissionDetailResponse.getComment());
        submission.setReferenceId(submissionDetailResponse.getReferenceId());
        submission.setRiskInsight(submissionDetailResponse.getRiskInsight());
        submission.setFleetSize(submissionDetailResponse.getFleetSize());
        submission.setNonImoCount(Objects.nonNull(submissionDetailResponse.getNonImoCount()) ? submissionDetailResponse.getNonImoCount() : 0);
        submission.setUnrecognizedVesselCount(Objects.nonNull(submissionDetailResponse.getUnrecognizedVesselCount()) ? submissionDetailResponse.getUnrecognizedVesselCount() : 0);
        submission.setUnavailableRiskScoreVesselCount(submissionDetailResponse.getUnavailableRiskScoreVesselCount());
        if (Objects.nonNull(submissionDetailResponse.getBrokerName())) {
            submission.setBrokerName(submissionDetailResponse.getBrokerName().getValue());
            submission.setBrokerNameProbability(submissionDetailResponse.getBrokerName().getProbability());
        }
        if (Objects.nonNull(submissionDetailResponse.getAccountName())) {
            submission.setAccountName(submissionDetailResponse.getAccountName().getValue());
            submission.setAccountNameProbability(submissionDetailResponse.getAccountName().getProbability());
        }
        if (Objects.nonNull(submissionDetailResponse.getBrokingHouse())) {
            submission.setBrokingHouse(submissionDetailResponse.getBrokingHouse().getValue());
            submission.setBrokingHouseProbability(submissionDetailResponse.getBrokingHouse().getProbability());
        }
        submission.setPolicyInfo(policyInfoMapper(submissionDetailResponse, objectMapper));
        submission.setVesselScheduleStatus(submissionDetailResponse.getVesselScheduleStatus());
        return submission;
    }

    private static List<Node> policyInfoMapper(SubmissionDetailResponse submissionDetailResponse, ObjectMapper objectMapper) {
        List<Node> nodes = new ArrayList<>();
        Map<String, Object> additionalInfo = submissionDetailResponse.getAdditionalInfo();
        if (Objects.isNull(additionalInfo)) {
            return nodes;
        }
        Object policyInfoObject = additionalInfo.get("policyInfo");
        if (Objects.isNull(policyInfoObject)) {
            return nodes;
        }
        Map<String, Object> policyInfoMap = objectMapper.convertValue(policyInfoObject, Map.class);
        for (Map.Entry<String, Object> entry : policyInfoMap.entrySet()) {
            Node node = new Node();
            node.setKey(entry.getKey());
            Object value = entry.getValue();
            if (Objects.nonNull(value)) {
                node.setValue(String.valueOf(value));
            }
            nodes.add(node);
        }
        return nodes;
    }

    public static Map<String, Object>  getPolicyInfoMap(SubmissionDetailResponse submissionDetailResponse, ObjectMapper objectMapper) {
        Map<String, Object> additionalInfo = submissionDetailResponse.getAdditionalInfo();
        if (Objects.isNull(additionalInfo)) {
            return new HashMap<>();
        }
        Object policyInfoObject = additionalInfo.get("policyInfo");
        if (Objects.isNull(policyInfoObject)) {
            return new HashMap<>();
        }
        Map<String, Object> policyInfoMap = objectMapper.convertValue(policyInfoObject, Map.class);
        return policyInfoMap;
    }
}
