package com.concirrus.submissions.graphql.model;

import com.concirrus.submissions.model.User;

import java.time.Instant;

public class Note {
    private final String id;
    private final User author;
    private final String time;
    private final String text;

    private final String noteType;
//    private final String editedOn;
//    private final String archivedOn;
//    private final boolean edited;
//    private final boolean isArchive;

    private final String subEntityId;
    private final String subEntityType;

    private final Instant updatedAt;
    private final String updatedBy;

    private final User updatedByAuthor;

    public String getId() {
        return id;
    }

    public User getAuthor() {
        return author;
    }

    public String getTime() {
        return time;
    }

    public String getText() {
        return text;
    }

    public String getNoteType() {
        return noteType;
    }

    public String getSubEntityId() {
        return subEntityId;
    }

    public String getSubEntityType() {
        return subEntityType;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public User getUpdatedByAuthor() {
        return updatedByAuthor;
    }

    //
//    public String getEditedOn() {
//        return editedOn;
//    }
//
//    public String getArchivedOn() {
//        return archivedOn;
//    }
//
//    public boolean isEdited() {
//        return edited;
//    }
//    public boolean getIsArchive() {
//        return isArchive;
//    }

    public Note(String id, User author, String time, String text) {
        this.id = id;
        this.author = author;
        this.time = time;
        this.text = text;
        this.noteType = null;
        this.subEntityId = null;
        this.subEntityType = null;
        this.updatedAt = null;
        this.updatedBy = null;
        this.updatedByAuthor = null;
//        this.editedOn = editedOn;
//        this.archivedOn = archivedOn;
//        this.edited = edited;
//        this.isArchive = isArchive;
    }
    public Note(String id, User author, String time, String text,String noteType) {
        this.id = id;
        this.author = author;
        this.time = time;
        this.text = text;
        this.noteType = noteType;
        this.subEntityId = null;
        this.subEntityType = null;
        this.updatedAt = null;
        this.updatedBy = null;
        this.updatedByAuthor = null;
//        this.editedOn = editedOn;
//        this.archivedOn = archivedOn;
//        this.edited = edited;
//        this.isArchive = isArchive;
    }

    public Note(String id, User author, String time, String text, String noteType, String subEntityId, String subEntityType) {
        this.id = id;
        this.author = author;
        this.time = time;
        this.text = text;
        this.noteType = noteType;
        this.subEntityId = subEntityId;
        this.subEntityType = subEntityType;
        this.updatedAt = null;
        this.updatedBy = null;
        this.updatedByAuthor = null;
    }


    public Note(String id, User author, String time, String text, String noteType, String subEntityId, String subEntityType, Instant updatedAt, String updatedBy) {
        this.id = id;
        this.author = author;
        this.time = time;
        this.text = text;
        this.noteType = noteType;
        this.subEntityId = subEntityId;
        this.subEntityType = subEntityType;
        this.updatedAt = updatedAt;
        this.updatedBy = updatedBy;
        this.updatedByAuthor = null;
    }

    public Note(String id, User author, String time, String text, String noteType, String subEntityId, String subEntityType, Instant updatedAt, String updatedBy, User updatedByAuthor) {
        this.id = id;
        this.author = author;
        this.time = time;
        this.text = text;
        this.noteType = noteType;
        this.subEntityId = subEntityId;
        this.subEntityType = subEntityType;
        this.updatedAt = updatedAt;
        this.updatedBy = updatedBy;
        this.updatedByAuthor = updatedByAuthor;
    }
}
