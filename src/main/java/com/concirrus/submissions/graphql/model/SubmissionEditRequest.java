package com.concirrus.submissions.graphql.model;

import com.concirrus.submission.common.validator.Content;
import com.concirrus.submission.common.validator.ContentTypeEnum;
import com.concirrus.submissions.common.CommonConstants;

import java.util.List;

public class SubmissionEditRequest {
    // added Content validations as per VAPT-471
    private List<VesselInfoInput> vessels;
    @Content(type = ContentTypeEnum.NO_HTML, maxLength = CommonConstants.MAX_CONTENT_LENGTH_100)
    private String brokerName;
    @Content(type = ContentTypeEnum.NO_HTML, maxLength = CommonConstants.MAX_CONTENT_LENGTH_100)
    private String productName;
    @Content(type = ContentTypeEnum.NO_HTML, maxLength = CommonConstants.MAX_CONTENT_LENGTH_100)
    private String accountName;
    @Content(type = ContentTypeEnum.NO_HTML, maxLength = CommonConstants.MAX_CONTENT_LENGTH_100)
    private  String productLine;
    @Content(type = ContentTypeEnum.NO_HTML, maxLength = CommonConstants.MAX_CONTENT_LENGTH_100)
    private String brokingHouse;
    private List<NodeInput> policyInfo;

    public SubmissionEditRequest() {
        //No Argument Constructor
    }

    public List<VesselInfoInput> getVessels() {
        return vessels;
    }

    public void setVessels(List<VesselInfoInput> vessels) {
        this.vessels = vessels;
    }

    public String getBrokerName() {
        return brokerName;
    }

    public void setBrokerName(String brokerName) {
        this.brokerName = brokerName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getProductLine() {
        return productLine;
    }

    public void setProductLine(String productLine) {
        this.productLine = productLine;
    }

    public String getBrokingHouse() {
        return brokingHouse;
    }

    public void setBrokingHouse(String brokingHouse) {
        this.brokingHouse = brokingHouse;
    }

    public List<NodeInput> getPolicyInfo() {
        return policyInfo;
    }

    public void setPolicyInfo(List<NodeInput> policyInfo) {
        this.policyInfo = policyInfo;
    }

}
