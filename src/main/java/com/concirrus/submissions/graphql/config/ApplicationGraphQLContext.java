package com.concirrus.submissions.graphql.config;

import com.concirrus.submissions.graphql.model.JsonScalarType;
import com.concirrus.submissions.graphql.security.SecurityContext;
import graphql.schema.GraphQLScalarType;
import graphql.servlet.GraphQLContext;
import org.dataloader.DataLoaderRegistry;
import org.springframework.context.annotation.Bean;

import javax.servlet.http.HttpServletRequest;

public class ApplicationGraphQLContext extends GraphQLContext {

  private final SecurityContext securityContext;

  public ApplicationGraphQLContext(HttpServletRequest httpServletRequest, SecurityContext securityContext) {
    super(httpServletRequest);
    this.securityContext = securityContext;
    this.setDataLoaderRegistry(new DataLoaderRegistry());
  }

//  public ApplicationGraphQLContext(Session session, HandshakeRequest handshakeRequest, SecurityContext securityContext) {
//    super(session, handshakeRequest);
//    this.securityContext = securityContext;
//    this.setDataLoaderRegistry(new DataLoaderRegistry());
//  }

  public SecurityContext getSecurityContext() {
    return securityContext;
  }

}
