package com.concirrus.submissions.graphql.security;

import java.util.*;
import java.util.stream.Stream;

public enum Role {

  POWERUSER("Power User");

  private final String code;
  private final Set<Role> subroles;
  private final Set<Role> roleWithSubroles;

  Role(String code) {
    this.code = code;
    this.subroles = Collections.emptySet();
    this.roleWithSubroles = createRoleWithSubroles(this, subroles);
  }

  Role(String code, Role... subroles) {
    this.code = code;
    this.subroles = new HashSet<>(Arrays.asList(subroles));
    this.roleWithSubroles = createRoleWithSubroles(this, this.subroles);

  }

  public String getCode() {
    return code;
  }

  public static Optional<Role> byCode(String code) {
    return Stream.of(Role.values())
        .filter(resource -> resource.code.equals(code))
        .findAny();
  }

  public Set<Role> getSubroles() {
    return subroles;
  }

  public Set<Role> getRoleWithSubroles() {
    return roleWithSubroles;
  }

  private static Set<Role> createRoleWithSubroles(Role role, Set<Role> subroles) {
    Set<Role> roleWithSubroles = new HashSet<>();
    roleWithSubroles.add(role);
    roleWithSubroles.addAll(subroles);
    return roleWithSubroles;
  }

}
