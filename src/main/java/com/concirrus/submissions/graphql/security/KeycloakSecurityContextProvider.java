package com.concirrus.submissions.graphql.security;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;

@Component
public class KeycloakSecurityContextProvider implements SecurityContextProvider {
    private static final Logger LOG = LoggerFactory.getLogger(KeycloakSecurityContextProvider.class);

    public static final String HEADER_USER_ID = "x-user-id";
    public static final String HEADER_USER_ROLES = "x-user-roles";
    private static final String X_QUEST_TOKEN = "x-quest-token";
    private static final String CSRF = "x-csrf-token";
    private static final String CSRF_VALID = "true";

    @Override
    public SecurityContext provide(HttpServletRequest httpServletRequest) {
        Optional<String> userIdHeaderValueOptional = Optional.ofNullable(httpServletRequest.getHeader(HEADER_USER_ID));
        if (!userIdHeaderValueOptional.isPresent()) {
            LOG.error("{} header value not present", HEADER_USER_ID);
            return SecurityContext.createAuthenticationError(SecurityContext.AuthenticationError.InvalidToken);
        }
//        String[] userRoles = httpServletRequest.getHeader(HEADER_USER_ROLES).split(",");
//        if (userRoles.length == 0) {
//            return SecurityContext.createAuthenticationError(SecurityContext.AuthenticationError.InvalidToken);
//        }

        String questToken = httpServletRequest.getHeader(X_QUEST_TOKEN);
        String csrfHeaderValue = httpServletRequest.getHeader(CSRF);

        Set<Role> roles = new HashSet<>();
//        for (String userRole : userRoles) {
//            Optional<Role> role = Role.byCode(userRole);
//            if (role.isPresent()) {
//                roles.add(role.get());
//            } else {
//                LOG.error("Unsupported role {}", userRole);
//            }
//        }

        return provide(userIdHeaderValueOptional.get(), questToken, roles, csrfHeaderValue);
    }

    private SecurityContext provide(String userId, String questToken, Set<Role> roles, String csrfHeaderValue) {
        User user = new User(userId, questToken, roles, CSRF_VALID.equalsIgnoreCase(csrfHeaderValue));
        return new SecurityContext(user, null);
    }
}