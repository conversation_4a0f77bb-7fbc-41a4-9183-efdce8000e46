package com.concirrus.submissions.graphql.security;

import java.util.Optional;


public class SecurityContext {

  public enum AuthenticationError {
    NoToken, InvalidToken, TokenExpired, InternalError
  }

  private final User user;
  private final AuthenticationError authenticationError;

  public SecurityContext(User user, AuthenticationError authenticationError) {
    this.user = user;
    this.authenticationError = authenticationError;
  }

  public static SecurityContext createLoggedIn(User user) {
    return new SecurityContext(user, null);
  }

  public static SecurityContext createAuthenticationError(AuthenticationError authenticationError) {
    return new SecurityContext(null, authenticationError);
  }

  public Optional<User> getUser() {
    return Optional.ofNullable(user);
  }

  public Optional<AuthenticationError> getAuthenticationError() {
    return Optional.ofNullable(authenticationError);
  }

  public boolean isUserLoggedIn() {
    return user != null;
  }

}
