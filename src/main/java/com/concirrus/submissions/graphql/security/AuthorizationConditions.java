package com.concirrus.submissions.graphql.security;

public final class AuthorizationConditions {

  private AuthorizationConditions() {
  }

  public static AuthorizationCondition hasRole(Role role) {
    return securityContext ->
        securityContext.getUser()
        .map(user -> user.getRoles().contains(role))
        .orElse(false);
  }

  public static AuthorizationCondition hasAtLeastRole(Role role) {
    return securityContext ->
        securityContext.getUser()
            .map(user -> user.getRolesWithSubroles().contains(role))
            .orElse(false);
  }

  public static AuthorizationCondition or(AuthorizationCondition condition1, AuthorizationCondition condition2) {
    return securityContext -> condition1.isFulfilled(securityContext) || condition2.isFulfilled(securityContext);
  }

}
