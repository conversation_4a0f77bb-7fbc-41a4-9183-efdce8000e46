package com.concirrus.submissions.graphql.security;


import com.concirrus.submissions.graphql.config.ApplicationGraphQLContext;
import com.concirrus.submissions.graphql.error.ClientMeaningfulFetchingException;
import com.concirrus.submissions.graphql.error.ForbiddenException;
import com.concirrus.submissions.graphql.error.NotAuthenticatedException;
import graphql.schema.DataFetchingEnvironment;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Optional;
import java.util.Set;

@Component
public class Sentry {

  /**
   * @return Id of logged in user
   */
  public String letPassLoggedInUser(DataFetchingEnvironment environment) {
    SecurityContext securityContext = environment.<ApplicationGraphQLContext>getContext().getSecurityContext();
    if (securityContext == null) {
      throw new RuntimeException("Security context is not available in this request!!");
    }
    return letPassLoggedInUserWhen(securityContext);
  }

  /**
   * @return logged in user
   */
  public User getLoggedInUser(DataFetchingEnvironment environment) {
    SecurityContext securityContext = environment.<ApplicationGraphQLContext>getContext().getSecurityContext();
    return getLoggedInUser(securityContext);
  }

  /**
   * @return logged in user
   */
  public User getLoggedInUser(SecurityContext securityContext) {
    if (securityContext == null) {
      throw new RuntimeException("Security context is not available in this request!!");
    }
    Optional<User> user = securityContext.getUser();
    if (!user.isPresent()) {
      throw new RuntimeException("Loggedin User is not found in this request!!");
    }
    return user.get();
  }

  /**
   * @return Id of logged in user
   */
  public String letPassLoggedInUserWhen(DataFetchingEnvironment environment, AuthorizationCondition ... conditions) {
    SecurityContext securityContext = environment.<ApplicationGraphQLContext>getContext().getSecurityContext();
    if (securityContext == null) {
      throw new RuntimeException("Security context is not available in this request!!");
    }
    return letPassLoggedInUserWhen(securityContext, conditions);
  }

  /**
   * @return Id of logged in user
   */
  public String letPassLoggedInUserWhen(SecurityContext securityContext, AuthorizationCondition ... conditions) {
    checkUserLoggedIn(securityContext);
    User user = securityContext.getUser().get();
    String userId = user.getId();

    if (!checkAuthorizationConditions(securityContext, conditions)) {
      throw new ForbiddenException();
    }

    return userId;
  }

  public boolean checkLoggedInUserAuthorizationConditions(DataFetchingEnvironment environment, AuthorizationCondition... conditions) {
    SecurityContext securityContext = environment.<ApplicationGraphQLContext>getContext().getSecurityContext();
    if (securityContext == null) {
      throw new RuntimeException("Security context is not available in this request!!");
    }
    return checkLoggedInUserAuthorizationConditions(securityContext, conditions);
  }

  public boolean checkLoggedInUserCsrfValid(DataFetchingEnvironment environment) {
    SecurityContext securityContext = environment.<ApplicationGraphQLContext>getContext().getSecurityContext();
    if (securityContext == null) {
      throw new RuntimeException("Security context is not available in this request!!");
    }
    Optional<User> user = securityContext.getUser();
    if (user.isPresent() && user.get().isCsrfValid()) {
      return true;
    }
    throw new ClientMeaningfulFetchingException("CSRF Validation Failed", "Forbidden");
  }

  public boolean checkLoggedInUserAuthorizationConditions(SecurityContext securityContext, AuthorizationCondition... conditions) {
    checkUserLoggedIn(securityContext);
    return checkAuthorizationConditions(securityContext, conditions);
  }

  private void checkUserLoggedIn(SecurityContext securityContext) {
    if (!securityContext.isUserLoggedIn()) {
      throw new NotAuthenticatedException(
          translateToMessage(securityContext.getAuthenticationError()));
    }
  }

  private boolean checkAuthorizationConditions(SecurityContext securityContext, AuthorizationCondition... conditions) {
    for (AuthorizationCondition condition : conditions) {
      if (!condition.isFulfilled(securityContext)) {
        return false;
      }
    }
    return true;
  }
  
  public Set<Role> getUserRoles (DataFetchingEnvironment environment) {
		SecurityContext securityContext = environment.<ApplicationGraphQLContext>getContext().getSecurityContext();
		if (securityContext == null) {
			return Collections.emptySet();
		}
	    return  securityContext.getUser().get().getRoles();
	}

  private static String translateToMessage(Optional<SecurityContext.AuthenticationError> authenticationError) {
    switch (authenticationError.orElse(SecurityContext.AuthenticationError.InternalError)) {
      case NoToken: return "No token.";
      case InvalidToken: return "Invalid token";
      case TokenExpired: return "Token expired";
      default: return "Authentication error";
    }
  }
}

