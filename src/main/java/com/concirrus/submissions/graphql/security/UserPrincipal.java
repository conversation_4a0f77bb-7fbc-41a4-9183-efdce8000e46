package com.concirrus.submissions.graphql.security;

import java.security.Principal;
import java.util.Set;

public class UserPrincipal implements Principal {
  private final String id;
  private final String name;
  private final String email;
  private final Set<String> roles;

  public UserPrincipal(String id, String name, String email, Set<String> roles) {
    this.id = id;
    this.name = name;
    this.email = email;
    this.roles = roles;
  }

  public String getId() {
    return id;
  }

  @Override
  public String getName() {
    return name;
  }

  public String getEmail() {
    return email;
  }

  public Set<String> getRoles() {
    return roles;
  }
}
