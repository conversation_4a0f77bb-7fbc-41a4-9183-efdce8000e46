package com.concirrus.submissions.graphql.security;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;

//TODO: Delete ME ->  Develop only - used to improve speed of frontend development
public class MockedSecurityContextProvider implements SecurityContextProvider {

  @Override
  public SecurityContext provide(HttpServletRequest httpServletRequest) {
    User user = new User("123", null, Collections.singleton(Role.POWERUSER), false);
    return SecurityContext.createLoggedIn(user);
  }

}
