package com.concirrus.submissions.graphql.security;

import java.util.Set;
import java.util.stream.Collectors;

public class User {
  private final String id;
  private final String questToken;
  private final Set<Role> roles;
  private final boolean csrfValid;

  public User(String id, String questToken, Set<Role> roles, boolean csrfValid) {
    this.id = id;
    this.questToken = questToken;
    this.roles = roles;
    this.csrfValid = csrfValid;
  }

  public String getQuestToken() {
    return questToken;
  }

  public String getId() {
    return id;
  }

  public Set<Role> getRoles() {
    return roles;
  }

  public boolean isCsrfValid() {
    return csrfValid;
  }

  public Set<Role> getRolesWithSubroles() {
    return roles.stream()
        .flatMap(role -> role.getRoleWithSubroles().stream())
        .collect(Collectors.toSet());
  }
}
