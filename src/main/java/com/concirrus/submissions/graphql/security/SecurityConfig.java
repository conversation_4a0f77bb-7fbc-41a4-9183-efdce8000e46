//package com.concirrus.submissions.graphql.security;
//
//
//import org.springframework.context.annotation.Configuration;
//import org.springframework.security.config.annotation.web.builders.HttpSecurity;
//import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
//
///**
// * This configuration disables spring boot auth to make authorization handled by {@link KeycloakSecurityContextProvider}.
// */
//@Configuration
//public class SecurityConfig extends WebSecurityConfigurerAdapter {
//
//  @Override
//  protected void configure(HttpSecurity http) throws Exception {
//    http.csrf().disable().authorizeRequests().anyRequest().permitAll().and().cors();
//  }
//}