package com.concirrus.submissions.graphql.security;//package com.concirrus.questmotor.service.insurerappbff.graphql.security;
//
//
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.PropertySource;
//import org.springframework.core.env.Environment;
//import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
//import org.springframework.security.core.Authentication;
//import org.springframework.security.jwt.crypto.sign.RsaVerifier;
//import org.springframework.security.oauth2.provider.token.DefaultAccessTokenConverter;
//import org.springframework.security.oauth2.provider.token.UserAuthenticationConverter;
//import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
//import org.springframework.security.oauth2.provider.token.store.JwtTokenStore;
//
//import java.util.*;
//
//@Configuration
//@PropertySource("classpath:/security.properties")
//public class KeycloakAuthorizationConfig {
//
//  private final Environment env;
//
//  public KeycloakAuthorizationConfig(Environment env) {
//    this.env = env;
//  }
//
//  @Bean
//  public JwtTokenStore jwtTokenStore() {
//    return new JwtTokenStore(jwtAccessTokenConverter());
//  }
//
//  private UserAuthenticationConverter userAuthenticationConverter() {
//    return new UserAuthenticationConverter() {
//      @Override
//      public Map<String, ?> convertUserAuthentication(Authentication userAuthentication) {
//        return null;
//      }
//
//      @Override
//      public Authentication extractAuthentication(Map<String, ?> map) {
//        String userId = map.get("sub").toString();
//        String name = "";
//        if (map.containsKey("name")) {
//          map.get("name").toString();
//        }
//        String email = "";
//        if (map.containsKey("email")) {
//          map.get("email").toString();
//        }
//        Set<String> roles = extractUserRoles(map);
//        UserPrincipal principal = new UserPrincipal(userId, name, email, roles);
//        return new UsernamePasswordAuthenticationToken(principal, "N/A", null);
//      }
//
//      private Set<String> extractUserRoles(Map<String, ?> map) {
//        Map<String, List<String>> realmAccess = (Map<String, List<String>>) map.get("realm_access");
//        List<String> rolesList = realmAccess != null ? realmAccess.get("roles") : Collections.emptyList();
//        return rolesList != null ? new HashSet<>(rolesList) : Collections.emptySet();
//      }
//    };
//  }
//
//  private JwtAccessTokenConverter jwtAccessTokenConverter() {
//    JwtAccessTokenConverter tokenConverter = new JwtAccessTokenConverter();
//    tokenConverter.setVerifier(new RsaVerifier(getVerifierKey()));
//    ((DefaultAccessTokenConverter) tokenConverter.getAccessTokenConverter()).setUserTokenConverter(userAuthenticationConverter());
//    return tokenConverter;
//  }
//
//  private String getVerifierKey() {
//    String key = env.getRequiredProperty("security.verifier-key");
//    if (key.startsWith("-----BEGIN")) {
//      return key;
//    }
//    return "-----BEGIN PUBLIC KEY-----\n" + key + "\n-----END PUBLIC KEY-----";
//  }
//}
