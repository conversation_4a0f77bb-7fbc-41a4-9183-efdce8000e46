package com.concirrus.submissions.model.aviation;

import com.concirrus.submissions.model.aviation.AviationPolicyInfo;
import com.concirrus.submissions.model.InsuredInfo;
import com.concirrus.submissions.model.Producer;
import com.concirrus.submissions.model.User;
import org.springframework.util.CollectionUtils;

import java.util.*;

public class AviationSubmissionSummary {
    private String submissionId;
    private String state;
    private String status;
    private String assigneeId;
    private String productName;
    private User assignedUser;

    private String businessHeading;
    private String proposedEffectiveDate;
    private Integer riskScore;
    private Integer numberOfInsuredAssets;
    private String businessLine;
    private String submissionNumber;
    private Integer fleetSize;
    private AviationPolicyInfo policyInfo;

    private String product;
    private List<InsuredInfo> insuredInfo;
    private Producer producer;
    private String receivedDate;
    private String blockedBySubmissionId;
    private Boolean isBlocked;
    private List<String> blockedSubmissions;

    public String getBusinessHeading() { return businessHeading;}

    public void setBusinessHeading(String businessHeading) { this.businessHeading = businessHeading;}

    public String getProposedEffectiveDate() { return proposedEffectiveDate;}

    public void setProposedEffectiveDate(String proposedEffectiveDate) { this.proposedEffectiveDate = proposedEffectiveDate;}

    public Integer getRiskScore() { return riskScore;}

    public void setRiskScore(Integer riskScore) { this.riskScore = riskScore;}

    public Integer getNumberOfInsuredAssets() { return numberOfInsuredAssets;}

    public void setNumberOfInsuredAssets(Integer numberOfInsuredAssets) { this.numberOfInsuredAssets = numberOfInsuredAssets;}

    public String getBusinessLine() { return businessLine;}

    public void setBusinessLine(String businessLine) { this.businessLine = businessLine;}

    public String getProduct() { return product;}

    public void setProduct(String product) { this.product = product;}

    public String getReceivedDate() { return receivedDate;}

    public void setReceivedDate(String receivedDate) { this.receivedDate = receivedDate;}

    public List<InsuredInfo> getInsuredInfo() { return insuredInfo;}

    public void setInsuredInfo(List<InsuredInfo> insuredInfo) {
        this.insuredInfo = insuredInfo;
    }

    public Producer getProducer() { return producer;}

    public void setProducer(Producer producer) { this.producer = producer;}

    public String getBlockedBySubmissionId() { return blockedBySubmissionId;}

    public void setBlockedBySubmissionId(String blockedBySubmissionId) { this.blockedBySubmissionId = blockedBySubmissionId;}

    public Boolean getBlocked() { return isBlocked;}

    public void setBlocked(Boolean blocked) { isBlocked = blocked;}

    public List<String> getBlockedSubmissions() { return blockedSubmissions;}

    public void setBlockedSubmissions(List<String> blockedSubmissions) {this.blockedSubmissions = blockedSubmissions;}

    public String getSubmissionNumber() { return submissionNumber; }

    public void setSubmissionNumber(String submissionNumber) { this.submissionNumber = submissionNumber; }

    public Integer getFleetSize() { return fleetSize; }

    public void setFleetSize(Integer fleetSize) { this.fleetSize = fleetSize; }

    public AviationPolicyInfo getPolicyInfo() { return policyInfo; }

    public void setPolicyInfo(AviationPolicyInfo policyInfo) { this.policyInfo = policyInfo; }


    public static AviationSubmissionSummary from(Map<String, Object> submission, String submissionId) {
        if (CollectionUtils.isEmpty(submission)) {
            return null;
        }
        AviationSubmissionSummary submissionSummary = new AviationSubmissionSummary();
        submissionSummary.setSubmissionId(submissionId);
        if(!submission.isEmpty() && submission.containsKey("state") && Objects.nonNull(submission.get("state"))){
            submissionSummary.setState(submission.get("state").toString());
        }
        if(!submission.isEmpty() && submission.containsKey("status") && Objects.nonNull(submission.get("status"))){
            submissionSummary.setStatus(submission.get("status").toString());
        }
        if(!submission.isEmpty() && submission.containsKey("assigneeId") && Objects.nonNull(submission.get("assigneeId"))){
            submissionSummary.setAssigneeId(submission.get("assigneeId").toString());
        }
        if(!submission.isEmpty() && submission.containsKey("productName") && Objects.nonNull(submission.get("productName"))){
            submissionSummary.setProductName(submission.get("productName").toString());
        }
        if(!submission.isEmpty()) {
            if(submission.containsKey("businessHeading") && Objects.nonNull(submission.get("businessHeading"))) {
                submissionSummary.setBusinessHeading(submission.get("businessHeading").toString());
            }
            if(submission.containsKey("proposedEffectiveDate") && Objects.nonNull(submission.get("proposedEffectiveDate"))) {
                submissionSummary.setProposedEffectiveDate(submission.get("proposedEffectiveDate").toString());
            }
            if(submission.containsKey("riskScore") && Objects.nonNull(submission.get("riskScore"))) {
                submissionSummary.setRiskScore(Integer.parseInt(submission.get("riskScore").toString()));
            }
            if(submission.containsKey("numberOfInsuredAssets") && Objects.nonNull(submission.get("numberOfInsuredAssets"))) {
                submissionSummary.setNumberOfInsuredAssets(Integer.parseInt(submission.get("numberOfInsuredAssets").toString()));
            }
            if(submission.containsKey("businessLine") && Objects.nonNull(submission.get("businessLine"))) {
                submissionSummary.setBusinessLine(submission.get("businessLine").toString());
            }
            if(submission.containsKey("submissionNumber") && Objects.nonNull(submission.get("submissionNumber"))) {
                submissionSummary.setSubmissionNumber(submission.get("submissionNumber").toString());
            }
            if(submission.containsKey("fleetSize") && Objects.nonNull(submission.get("fleetSize"))) {
                submissionSummary.setFleetSize(Integer.parseInt(submission.get("fleetSize").toString()));
            }
            if(submission.containsKey("product") && Objects.nonNull(submission.get("product"))) {
                submissionSummary.setProduct(submission.get("product").toString());
            }
            if(submission.containsKey("blockedBySubmissionId") && Objects.nonNull(submission.get("blockedBySubmissionId"))) {
                submissionSummary.setBlockedBySubmissionId(submission.get("blockedBySubmissionId").toString());
            }
            if(submission.containsKey("isBlocked") && Objects.nonNull(submission.get("isBlocked"))) {
                submissionSummary.setBlocked((Boolean) submission.get("isBlocked"));
            }
            if (submission.containsKey("blockedSubmissions") && Objects.nonNull(submission.get("blockedSubmissions"))) {
                Object blockedSubmissionsObject = submission.get("blockedSubmissions");
                if (blockedSubmissionsObject instanceof List<?>) {
                    List<String> blockedSubmissionsList = (List<String>) blockedSubmissionsObject;
                    submissionSummary.setBlockedSubmissions(blockedSubmissionsList);
                }
            }

            if (submission.containsKey("insuredInfo") && submission.get("insuredInfo") instanceof List) {
                List<InsuredInfo> insuredInfoList = new ArrayList<>();
                List<Object> submittedInsuredInfoList = (List<Object>) submission.get("insuredInfo");

                for (Object item : submittedInsuredInfoList) {
                    if (item instanceof Map) {
                        Map<String, Object> itemMap = (Map<String, Object>) item;
                        if (itemMap.containsKey("name") && Objects.nonNull(itemMap.get("name"))) {
                            InsuredInfo insuredInfo = new InsuredInfo();
                            insuredInfo.setName(itemMap.get("name").toString()); // Assuming "name" is a String
                            insuredInfoList.add(insuredInfo);
                        }
                    }
                }

                submissionSummary.setInsuredInfo(insuredInfoList);
            }

            if(submission.containsKey("policyInfo") && Objects.nonNull(submission.get("policyInfo"))) {
                Map<String, Object> policyInfo = (Map<String, Object>) submission.get("policyInfo");
                AviationPolicyInfo policyInfoDetails = mapToAviationPolicyInfo(policyInfo);
                submissionSummary.setPolicyInfo(policyInfoDetails);
            }
            if(submission.containsKey("producer") && Objects.nonNull(submission.get("producer"))) {
                Map<String, Object> producer = (Map<String, Object>) submission.get("producer");
                Producer producerDetails = mapToProducer(producer);
                submissionSummary.setProducer(producerDetails);
            }
            if(submission.containsKey("receivedDate") && Objects.nonNull(submission.get("receivedDate"))) {
                submissionSummary.setReceivedDate(submission.get("receivedDate").toString());
            }
        }
        return submissionSummary;
    }


    private static AviationPolicyInfo mapToAviationPolicyInfo(Map<String, Object> policyInfoMap) {
        AviationPolicyInfo policyInfo = new AviationPolicyInfo();

        policyInfo.setRequestedEffectiveDate(getString(policyInfoMap, "requestedEffectiveDate"));
        return policyInfo;
    }

    private static Producer mapToProducer(Map<String, Object> producerMap) {
        Producer producer = new Producer();

        producer.setName(getString(producerMap, "name"));
        producer.setAgencyName(getString(producerMap, "agencyName"));
        producer.setPhone(getString(producerMap, "phone"));
        producer.setEmail(getString(producerMap, "email"));
        producer.setAddress(getString(producerMap, "address"));
        producer.setCity(getString(producerMap, "city"));
        producer.setState(getString(producerMap, "state"));
        producer.setZip(getString(producerMap, "zip"));

        return producer;
    }

    private static String getString(Map<String, Object> map, String key) {
        if (map.containsKey(key) && Objects.nonNull(map.get(key))) {
            return map.get(key).toString();
        }
        return null;
    }

    public User getAssignedUser() {
        return assignedUser;
    }

    public void setAssignedUser(User assignedUser) {
        this.assignedUser = assignedUser;
    }

    public String getAssigneeId() {
        return assigneeId;
    }

    public void setAssigneeId(String assigneeId) {
        this.assigneeId = assigneeId;
    }

    public String getSubmissionId() {
        return submissionId;
    }

    public void setSubmissionId(String submissionId) {
        this.submissionId = submissionId;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }
}
