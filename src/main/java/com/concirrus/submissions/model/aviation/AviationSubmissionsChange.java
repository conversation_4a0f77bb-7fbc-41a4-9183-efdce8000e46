package com.concirrus.submissions.model.aviation;

import java.util.Map;

public class AviationSubmissionsChange {
    String submissionId;
    String updateType;
    AviationSubmissionSummary updatedSubmission;
    AviationSubmissionSummary previousSubmission;

    public AviationSubmissionsChange(String submissionId, String updateType, AviationSubmissionSummary updatedSubmission, AviationSubmissionSummary previousSubmission) {
        this.submissionId = submissionId;
        this.updateType = updateType;
        this.updatedSubmission = updatedSubmission;
        this.previousSubmission = previousSubmission;
    }

    public String getSubmissionId() {
        return submissionId;
    }

    public void setSubmissionId(String submissionId) {
        this.submissionId = submissionId;
    }

    public String getUpdateType() {
        return updateType;
    }

    public void setUpdateType(String updateType) {
        this.updateType = updateType;
    }

    public AviationSubmissionSummary getUpdatedSubmission() {
        return updatedSubmission;
    }

    public void setUpdatedSubmission(AviationSubmissionSummary updatedSubmission) {
        this.updatedSubmission = updatedSubmission;
    }

    public AviationSubmissionSummary getPreviousSubmission() {
        return previousSubmission;
    }

    public void setPreviousSubmission(AviationSubmissionSummary previousSubmission) {
        this.previousSubmission = previousSubmission;
    }
}
