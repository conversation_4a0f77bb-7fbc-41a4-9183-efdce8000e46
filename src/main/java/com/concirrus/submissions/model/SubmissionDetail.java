package com.concirrus.submissions.model;

import com.concirrus.submission.connector.submissionmanager.model.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class SubmissionDetail {
    private String submissionId;
    private SubmissionState submissionState;
    private SubmissionStatus status;
    private User assignedUser;
    private List<String> imos;
    private List<VesselInfo> vessels;
    private String brokerName;
    private Double brokerNameProbability = 0.0;
    private String productName;
    private String accountName;
    private Double accountNameProbability = 0.0;
    private int fleetSize;
    private int unrecognizedVesselCount;
    private int nonImoCount;
    private Instant receivedDate;
    private Instant timeUpdated;
    private String productLine;
    private String brokingHouse;
    private Double brokingHouseProbability = 0.0;
    private String comment;
    private String referenceId;
    private String clientId;
    private RiskInsight riskInsight;
    private VesselScheduleStatus vesselScheduleStatus;
    private Integer unavailableRiskScoreVesselCount;
    private com.concirrus.submissions.graphql.model.PolicyInfo policyInfo;
    private static ObjectMapper mapper = new ObjectMapper();

    public static SubmissionDetail getInstance(Submission submission, User user,RiskInsight riskInsight) {
        if(Objects.isNull(submission))
        {
            return null;
        }
        SubmissionDetail submissionDetail= new SubmissionDetail();
        submissionDetail.setRiskInsight(riskInsight);
        submissionDetail.setAssignedUser(user);
        submissionDetail.setComment(submission.getComment());
        if (Objects.nonNull(submission.getBrokerName())) {
            submissionDetail.setBrokerName(submission.getBrokerName().getValue());
            submissionDetail.setBrokerNameProbability(submission.getBrokerName().getProbability());
        }
        submissionDetail.setSubmissionId(submission.getId());
        submissionDetail.setSubmissionState(submission.getState());
        submissionDetail.setStatus(submission.getStatus());
        if(!CollectionUtils.isEmpty(submission.getVessels())) {
            submissionDetail.setImos(
                    submission.getVessels().stream()
                            .map(VesselInfo::getImo)
                            .distinct()
                            .collect(Collectors.toList())
            );
            submissionDetail.setVessels(submission.getVessels());
        } else {
            submissionDetail.setImos(Collections.emptyList());
            submissionDetail.setVessels(Collections.emptyList());
        }
        if (Objects.nonNull(submission.getAccountName())) {
            submissionDetail.setAccountName(submission.getAccountName().getValue());
            submissionDetail.setAccountNameProbability(submission.getAccountName().getProbability());
        }
        if (Objects.nonNull(submission.getBrokingHouse())) {
            submissionDetail.setBrokingHouse(submission.getBrokingHouse().getValue());
            submissionDetail.setBrokingHouseProbability(submission.getBrokingHouse().getProbability());
        }
        submissionDetail.setClientId(submission.getClientId());
        submissionDetail.setProductLine(submission.getProductLine());
        submissionDetail.setProductName(submission.getProductName());
        submissionDetail.setReceivedDate(submission.getReceivedDate());
        submissionDetail.setTimeUpdated(submission.getUpdatedAt());
        submissionDetail.setReferenceId(submission.getReferenceId());
        submissionDetail.setFleetSize(submission.getFleetSize());
        submissionDetail.setNonImoCount(submission.getNonImoCount());
        submissionDetail.setUnrecognizedVesselCount(submission.getUnrecognizedVesselCount());
        submissionDetail.setVesselScheduleStatus(submission.getVesselScheduleStatus());
        submissionDetail.setUnavailableRiskScoreVesselCount(submission.getUnavailableRiskScoreVesselCount());
        if(submission.getPolicyInfo()!=null){
            PolicyInfo policyInfoObj = submission.getPolicyInfo();
            com.concirrus.submissions.graphql.model.PolicyInfo policyInfo = new com.concirrus.submissions.graphql.model.PolicyInfo();
            policyInfo.setPolicyEndDate(policyInfoObj.getPolicyEndDate());
            policyInfo.setPolicyStartDate(policyInfoObj.getPolicyStartDate());
            policyInfo.setBrokeragePercentage(policyInfoObj.getBrokeragePercentage());
            policyInfo.setTotalChargedPremium(policyInfoObj.getTotalChargedPremium());
            submissionDetail.setPolicyInfo(policyInfo);
        }
        return submissionDetail;
    }

    public int getUnrecognizedVesselCount() {
        return unrecognizedVesselCount;
    }

    public void setUnrecognizedVesselCount(int unrecognizedVesselCount) {
        this.unrecognizedVesselCount = unrecognizedVesselCount;
    }

    public int getNonImoCount() {
        return nonImoCount;
    }

    public void setNonImoCount(int nonImoCount) {
        this.nonImoCount = nonImoCount;
    }

    public String getSubmissionId() {
        return submissionId;
    }

    public void setSubmissionId(String submissionId) {
        this.submissionId = submissionId;
    }

    public SubmissionState getSubmissionState() {
        return submissionState;
    }

    public void setSubmissionState(SubmissionState submissionState) {
        this.submissionState = submissionState;
    }

    public SubmissionStatus getStatus() {
        return status;
    }

    public void setStatus(SubmissionStatus status) {
        this.status = status;
    }

    public User getAssignedUser() {
        return assignedUser;
    }

    public void setAssignedUser(User assignedUser) {
        this.assignedUser = assignedUser;
    }

    public List<String> getImos() {
        return imos;
    }

    public void setImos(List<String> imos) {
        this.imos = imos;
    }

    public List<VesselInfo> getVessels() {
        return vessels;
    }

    public void setVessels(List<VesselInfo> vessels) {
        this.vessels = vessels;
    }

    public String getBrokerName() {
        return brokerName;
    }

    public void setBrokerName(String brokerName) {
        this.brokerName = brokerName;
    }

    public Double getBrokerNameProbability() {
        return brokerNameProbability;
    }

    public void setBrokerNameProbability(Double brokerNameProbability) {
        this.brokerNameProbability = brokerNameProbability;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public Double getAccountNameProbability() {
        return accountNameProbability;
    }

    public void setAccountNameProbability(Double accountNameProbability) {
        this.accountNameProbability = accountNameProbability;
    }

    public int getFleetSize() {
        return fleetSize;
    }

    public void setFleetSize(int fleetSize) {
        this.fleetSize = fleetSize;
    }

    public Instant getReceivedDate() {
        return receivedDate;
    }

    public void setReceivedDate(Instant receivedDate) {
        this.receivedDate = receivedDate;
    }

    public Instant getTimeUpdated() {
        return timeUpdated;
    }

    public void setTimeUpdated(Instant timeUpdated) {
        this.timeUpdated = timeUpdated;
    }

    public String getProductLine() {
        return productLine;
    }

    public void setProductLine(String productLine) {
        this.productLine = productLine;
    }

    public String getBrokingHouse() {
        return brokingHouse;
    }

    public void setBrokingHouse(String brokingHouse) {
        this.brokingHouse = brokingHouse;
    }

    public Double getBrokingHouseProbability() {
        return brokingHouseProbability;
    }

    public void setBrokingHouseProbability(Double brokingHouseProbability) {
        this.brokingHouseProbability = brokingHouseProbability;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(String referenceId) {
        this.referenceId = referenceId;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public RiskInsight getRiskInsight() {
        return riskInsight;
    }

    public void setRiskInsight(RiskInsight riskInsight) {
        this.riskInsight = riskInsight;
    }

    public VesselScheduleStatus getVesselScheduleStatus() {
        return vesselScheduleStatus;
    }

    public void setVesselScheduleStatus(VesselScheduleStatus vesselScheduleStatus) {
        this.vesselScheduleStatus = vesselScheduleStatus;
    }

    public Integer getUnavailableRiskScoreVesselCount() {
        return unavailableRiskScoreVesselCount;
    }

    public void setUnavailableRiskScoreVesselCount(Integer unavailableRiskScoreVesselCount) {
        this.unavailableRiskScoreVesselCount = unavailableRiskScoreVesselCount;
    }

    public com.concirrus.submissions.graphql.model.PolicyInfo getPolicyInfo() {
        return policyInfo;
    }

    public void setPolicyInfo(com.concirrus.submissions.graphql.model.PolicyInfo policyInfo) {
        this.policyInfo = policyInfo;
    }
}
