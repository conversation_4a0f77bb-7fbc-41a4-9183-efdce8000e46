package com.concirrus.submissions.model.repository.hash;

import org.springframework.data.annotation.Id;
import org.springframework.data.redis.core.RedisHash;

import java.util.List;

@RedisHash("ClientFeature")
public class ClientFeatureHash {
    @Id
    private String clientId;
    private List<String> allowedFeatures;

    public ClientFeatureHash() {
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public List<String> getAllowedFeatures() {
        return allowedFeatures;
    }

    public void setAllowedFeatures(List<String> allowedFeatures) {
        this.allowedFeatures = allowedFeatures;
    }
}
