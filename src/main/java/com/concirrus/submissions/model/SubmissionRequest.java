package com.concirrus.submissions.model;

import java.util.List;

public class SubmissionRequest {

    private List<String> imos;
    private String brokerName;
    private String productName;
    private String accountName;
    private  String productLine;
    private String brokingHouse;
    private  String comment;
    private String clientId;

    public List<String> getImos() {
        return imos;
    }

    public void setImos(List<String> imos) {
        this.imos = imos;
    }

    public String getBrokerName() {
        return brokerName;
    }

    public void setBrokerName(String brokerName) {
        this.brokerName = brokerName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getProductLine() {
        return productLine;
    }

    public void setProductLine(String productLine) {
        this.productLine = productLine;
    }

    public String getBrokingHouse() {
        return brokingHouse;
    }

    public void setBrokingHouse(String brokingHouse) {
        this.brokingHouse = brokingHouse;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }


}
