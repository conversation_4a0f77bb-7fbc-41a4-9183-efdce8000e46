package com.concirrus.submissions.model;

import com.concirrus.submission.common.validator.Content;
import com.concirrus.submission.common.validator.ContentTypeEnum;
import com.concirrus.submissions.common.CommonConstants;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.List;
import java.util.stream.Collectors;

public class SubmissionSearchRequest {
    List<FleetSizeRange> fleetSizeRange;
    List<String> productNames;
    List<String> accountNames;
    List<String> statusList;
    LocalDate startDate;
    LocalDate endDate;
    @Content(type = ContentTypeEnum.NO_HTML, maxLength = CommonConstants.MAX_CONTENT_LENGTH_100)
    String searchText;
    List<String> stateList;
    List<Integer> riskScores;
    Boolean showArchived = Boolean.FALSE;
    LocalDate inceptionFromDate;
    LocalDate inceptionToDate;

    public List<FleetSizeRange> getFleetSizeRange() {
        return fleetSizeRange;
    }

    public void setFleetSizeRange(List<FleetSizeRange> fleetSizeRange) {
        this.fleetSizeRange = fleetSizeRange;
    }

    public List<String> getProductNames() {
        return productNames;
    }

    public void setProductNames(List<String> productNames) {
        this.productNames = productNames;
    }

    public List<String> getAccountNames() {
        return accountNames;
    }

    public void setAccountNames(List<String> accountNames) {
        this.accountNames = accountNames;
    }

    public List<String> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<String> statusList) {
        this.statusList = statusList;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public String getSearchText() {
        return searchText;
    }

    public void setSearchText(String searchText) {
        this.searchText = searchText;
    }

    public List<String> getStateList() {
        return stateList;
    }

    public void setStateList(List<String> stateList) {
        this.stateList = stateList;
    }

    public List<Integer> getRiskScores() {
        return riskScores;
    }

    public void setRiskScores(List<Integer> riskScores) {
        this.riskScores = riskScores;
    }

    public Boolean getShowArchived() {
        return showArchived;
    }

    public void setShowArchived(Boolean showArchived) {
        this.showArchived = showArchived;
    }

    public LocalDate getInceptionFromDate() {
        return inceptionFromDate;
    }

    public void setInceptionFromDate(LocalDate inceptionFromDate) {
        this.inceptionFromDate = inceptionFromDate;
    }

    public LocalDate getInceptionToDate() {
        return inceptionToDate;
    }

    public void setInceptionToDate(LocalDate inceptionToDate) {
        this.inceptionToDate = inceptionToDate;
    }

    public static com.concirrus.submission.connector.submissionmanager.model.SubmissionSearchRequest getSearchRequestForQuest(SubmissionSearchRequest submissionSearchRequest, List<String> searchedUserIds)
    {
        com.concirrus.submission.connector.submissionmanager.model.SubmissionSearchRequest request = new com.concirrus.submission.connector.submissionmanager.model.SubmissionSearchRequest();
        request.setAccountNames(submissionSearchRequest.getAccountNames());
        request.setShowArchived(submissionSearchRequest.getShowArchived());
        if(submissionSearchRequest.startDate != null && submissionSearchRequest.startDate.equals(submissionSearchRequest.endDate))
        {
            //When start date and end date is same range should start from start day till next day start is same the date
            request.setStartDate(submissionSearchRequest.startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            request.setEndDate(submissionSearchRequest.endDate.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        }else {
            if (submissionSearchRequest.endDate != null) {
                Instant endDateInstant = submissionSearchRequest.endDate.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant();
                request.setEndDate(endDateInstant);
            }
            if (submissionSearchRequest.startDate != null) {
                Instant startDateInstant = submissionSearchRequest.startDate.atStartOfDay(ZoneId.systemDefault()).toInstant();
                request.setStartDate(startDateInstant);
            }
        }
        if(!CollectionUtils.isEmpty(submissionSearchRequest.getFleetSizeRange())) {
            request.setFleetSizeRange(submissionSearchRequest.getFleetSizeRange().stream().map(x->new com.concirrus.submission.connector.submissionmanager.model.FleetSizeRange(x.getFromFleetSize(),x.toFleetSize)).collect(Collectors.toList()));
        }
        if(!CollectionUtils.isEmpty(submissionSearchRequest.getRiskScores())) {
            request.setRiskScores(submissionSearchRequest.getRiskScores());
        }
        if(!CollectionUtils.isEmpty(searchedUserIds)) {
            request.setAssigneeIds(searchedUserIds);
        }
        request.setStateList(submissionSearchRequest.stateList);
        request.setSearchText(submissionSearchRequest.searchText);
        request.setProductNames(submissionSearchRequest.productNames);
        request.setStatusList(submissionSearchRequest.statusList);
        request.setInceptionFromDate(submissionSearchRequest.getInceptionFromDate());
        request.setInceptionToDate(submissionSearchRequest.getInceptionToDate());
        return request;
    }

    public static com.concirrus.submission.connector.submissionmanager.model.SubmissionSearchRequest getSearchRequestForQuest(SubmissionSearchRequest submissionSearchRequest) {
        com.concirrus.submission.connector.submissionmanager.model.SubmissionSearchRequest request = new com.concirrus.submission.connector.submissionmanager.model.SubmissionSearchRequest();
        request.setAccountNames(submissionSearchRequest.getAccountNames());
        request.setShowArchived(submissionSearchRequest.getShowArchived());
        if(submissionSearchRequest.startDate != null && submissionSearchRequest.startDate.equals(submissionSearchRequest.endDate))
        {
            //When start date and end date is same range should start from start day till next day start is same the date
            request.setStartDate(submissionSearchRequest.startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            request.setEndDate(submissionSearchRequest.endDate.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        }else {
            if (submissionSearchRequest.endDate != null) {
                Instant endDateInstant = submissionSearchRequest.endDate.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant();
                request.setEndDate(endDateInstant);
            }
            if (submissionSearchRequest.startDate != null) {
                Instant startDateInstant = submissionSearchRequest.startDate.atStartOfDay(ZoneId.systemDefault()).toInstant();
                request.setStartDate(startDateInstant);
            }
        }
        if(!CollectionUtils.isEmpty(submissionSearchRequest.getFleetSizeRange())) {
            request.setFleetSizeRange(submissionSearchRequest.getFleetSizeRange().stream().map(x->new com.concirrus.submission.connector.submissionmanager.model.FleetSizeRange(x.getFromFleetSize(),x.toFleetSize)).collect(Collectors.toList()));
        }
        if(!CollectionUtils.isEmpty(submissionSearchRequest.getRiskScores())) {
            request.setRiskScores(submissionSearchRequest.getRiskScores());
        }
        request.setStateList(submissionSearchRequest.stateList);
        request.setSearchText(submissionSearchRequest.searchText);
        request.setProductNames(submissionSearchRequest.productNames);
        request.setStatusList(submissionSearchRequest.statusList);
        request.setInceptionFromDate(submissionSearchRequest.getInceptionFromDate());
        request.setInceptionToDate(submissionSearchRequest.getInceptionToDate());
        return request;
    }
}
