package com.concirrus.submissions.model;

import com.concirrus.submission.connector.submissionmanager.model.SubmissionChangeEventDto;
import com.concirrus.submission.connector.submissionmanager.model.SubmissionEvent;
import com.concirrus.submission.connector.submissionmanager.model.SubmissionState;
import com.concirrus.submission.connector.submissionmanager.model.SubmissionStatus;

import java.time.Instant;
import java.util.Objects;

public class SubmissionSummary {
    private String submissionId;
    private SubmissionState submissionState;
    private SubmissionStatus status;
    private String brokerName;
    private String productName;
    private String accountName;
    private String brokingHouse;

    public String getSubmissionId() {
        return submissionId;
    }

    public void setSubmissionId(String submissionId) {
        this.submissionId = submissionId;
    }

    public SubmissionState getSubmissionState() {
        return submissionState;
    }

    public void setSubmissionState(SubmissionState submissionState) {
        this.submissionState = submissionState;
    }

    public SubmissionStatus getStatus() {
        return status;
    }

    public void setStatus(SubmissionStatus status) {
        this.status = status;
    }

    public String getBrokerName() {
        return brokerName;
    }

    public void setBrokerName(String brokerName) {
        this.brokerName = brokerName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getBrokingHouse() {
        return brokingHouse;
    }

    public void setBrokingHouse(String brokingHouse) {
        this.brokingHouse = brokingHouse;
    }

    public static SubmissionSummary from(SubmissionChangeEventDto submissionChangeEventDto) {
        if (Objects.isNull(submissionChangeEventDto.getPreviousSubmission())) {
            return null;
        }
        SubmissionSummary submissionSummary = new SubmissionSummary();
        submissionSummary.setSubmissionId(submissionChangeEventDto.getSubmissionId());
        SubmissionEvent previousSubmission = submissionChangeEventDto.getPreviousSubmission();
        submissionSummary.setSubmissionState(previousSubmission.getState());
        submissionSummary.setStatus(previousSubmission.getStatus());

        if (Objects.nonNull(previousSubmission.getAccountName())) {
            submissionSummary.setAccountName(previousSubmission.getAccountName().getValue());
        }
        if (Objects.nonNull(previousSubmission.getBrokingHouse())) {
            submissionSummary.setBrokingHouse(previousSubmission.getBrokingHouse().getValue());
        }
        if (Objects.nonNull(previousSubmission.getBrokerName())) {
            submissionSummary.setBrokerName(previousSubmission.getBrokerName().getValue());
        }
        submissionSummary.setProductName(previousSubmission.getProductName());
        return submissionSummary;
    }
}