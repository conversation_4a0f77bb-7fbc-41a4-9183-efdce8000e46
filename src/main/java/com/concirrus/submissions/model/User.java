package com.concirrus.submissions.model;

import com.concirrus.submission.connector.accessmanagement.model.UserInfoResponse;
import com.concirrus.submission.connector.accessmanagement.model.UserResponseDto;
import com.concirrus.submissions.connector.questinsights.model.user.UserInfo;
import com.concirrus.submissions.connector.questinsights.model.user.UserSite;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

public class User {
    private String userId;
    private String firstName;
    private String lastName;
    private List<UserSite> userSites;

    public static User getInstance(UserInfo userInfo) {
        User user = new User();
        if(Objects.nonNull(userInfo)) {
            user.setUserId(userInfo.getUserId());
            user.setFirstName(userInfo.getFirstName());
            user.setLastName(userInfo.getLastName());
            if (CollectionUtils.isEmpty(userInfo.getUserSites())) {
                user.setUserSites(Collections.emptyList());
            } else {
                user.setUserSites(userInfo.getUserSites());
            }
        }
        return user;
    }

    public static User getInstance(UserInfoResponse userInfoResponse) {
        User user = new User();
        if(Objects.nonNull(userInfoResponse)) {
            user.setUserId(userInfoResponse.getId());
            user.setFirstName(userInfoResponse.getFirstName());
            user.setLastName(userInfoResponse.getLastName());
        }
        return user;
    }

    public static User getInstance(UserResponseDto userInfoResponse) {
        User user = new User();
        if(Objects.nonNull(userInfoResponse)) {
            user.setUserId(userInfoResponse.getUserId());
            user.setFirstName(userInfoResponse.getFirstName());
            user.setLastName(userInfoResponse.getLastName());
        }
        return user;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public List<UserSite> getUserSites() {
        return userSites;
    }

    public void setUserSites(List<UserSite> userSites) {
        this.userSites = userSites;
    }
}
