package com.concirrus.submissions.model.aviationwar;

import java.util.List;

public class AviationWarInsuredInfo {

    private String insuredName;
    private List<Object> additionalInsureds;
    private String city;
    private String coverType;
    private String domicile;
    private String state;
    private String street;
    private String website;
    private String zip;

    public String getInsuredName() {
        return insuredName;
    }

    public void setInsuredName(String insuredName) {
        this.insuredName = insuredName;
    }

    public List<Object> getAdditionalInsureds() {
        return additionalInsureds;
    }

    public void setAdditionalInsureds(List<Object> additionalInsureds) {
        this.additionalInsureds = additionalInsureds;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCoverType() {
        return coverType;
    }

    public void setCoverType(String coverType) {
        this.coverType = coverType;
    }

    public String getDomicile() {
        return domicile;
    }

    public void setDomicile(String domicile) {
        this.domicile = domicile;
    }


    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public String getZip() {
        return zip;
    }

    public void setZip(String zip) {
        this.zip = zip;
    }
}
