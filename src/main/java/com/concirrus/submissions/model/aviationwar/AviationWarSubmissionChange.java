package com.concirrus.submissions.model.aviationwar;

import java.util.Map;

public class AviationWarSubmissionChange {
    private String submissionId;
    private String updateType;
    private AviationWarSubmissionSummary previousSubmission;
    private AviationWarSubmissionSummary updatedSubmission;

    public AviationWarSubmissionChange(String submissionId, String updateType, AviationWarSubmissionSummary previousSubmission, AviationWarSubmissionSummary updatedSubmission) {
        this.submissionId = submissionId;
        this.updateType = updateType;
        this.previousSubmission = previousSubmission;
        this.updatedSubmission = updatedSubmission;
    }

    public AviationWarSubmissionChange() {
    }

    public String getSubmissionId() {
        return submissionId;
    }

    public void setSubmissionId(String submissionId) {
        this.submissionId = submissionId;
    }

    public String getUpdateType() {
        return updateType;
    }

    public void setUpdateType(String updateType) {
        this.updateType = updateType;
    }

    public AviationWarSubmissionSummary getPreviousSubmission() {
        return previousSubmission;
    }

    public void setPreviousSubmission(AviationWarSubmissionSummary previousSubmission) {
        this.previousSubmission = previousSubmission;
    }

    public AviationWarSubmissionSummary getUpdatedSubmission() {
        return updatedSubmission;
    }

    public void setUpdatedSubmission(AviationWarSubmissionSummary updatedSubmission) {
        this.updatedSubmission = updatedSubmission;
    }
}
