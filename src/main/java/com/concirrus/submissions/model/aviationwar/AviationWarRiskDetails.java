package com.concirrus.submissions.model.aviationwar;

import java.util.List;

public class AviationWarRiskDetails {
    private List<String> peril;
    private String expiryDate;
    private String inceptionDate;
    private String occupancy;
    private Double hullLimit;
    private String policyCurrency;
    private String premiumCurrency;
    private Double quotedWarPremium;
    private Double boundWarPremium;
    private Double tlo;
    private Double hijacking;
    private Double technicalRecords;
    private Double maxHullLimit;
    private Double aggregateLimit;
    private Double quotedRate;
    private Double boundRate;
    private Double excess;
    private String hubAirport;
    private Double totalFleetValue;
    private Double averageFleetValue;
    private Double averageAircraftValue;

    public AviationWarRiskDetails(List<String> peril, String expiryDate, String inceptionDate, String occupancy, Double hullLimit, String policyCurrency, String premiumCurrency, Double quotedWarPremium, Double boundWarPremium, Double tlo, Double hijacking, Double technicalRecords, Double maxHullLimit, Double aggregateLimit, Double quotedRate, Double boundRate, Double excess, String hubAirport, Double totalFleetValue, Double averageFleetValue, Double averageAircraftValue) {
        this.peril = peril;
        this.expiryDate = expiryDate;
        this.inceptionDate = inceptionDate;
        this.occupancy = occupancy;
        this.hullLimit = hullLimit;
        this.policyCurrency = policyCurrency;
        this.premiumCurrency = premiumCurrency;
        this.quotedWarPremium = quotedWarPremium;
        this.boundWarPremium = boundWarPremium;
        this.tlo = tlo;
        this.hijacking = hijacking;
        this.technicalRecords = technicalRecords;
        this.maxHullLimit = maxHullLimit;
        this.aggregateLimit = aggregateLimit;
        this.quotedRate = quotedRate;
        this.boundRate = boundRate;
        this.excess = excess;
        this.hubAirport = hubAirport;
        this.totalFleetValue = totalFleetValue;
        this.averageFleetValue = averageFleetValue;
        this.averageAircraftValue = averageAircraftValue;
    }

    public AviationWarRiskDetails() {
    }

    public List<String> getPeril() {
        return peril;
    }

    public void setPeril(List<String> peril) {
        this.peril = peril;
    }

    public String getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(String expiryDate) {
        this.expiryDate = expiryDate;
    }

    public String getInceptionDate() {
        return inceptionDate;
    }

    public void setInceptionDate(String inceptionDate) {
        this.inceptionDate = inceptionDate;
    }

    public String getOccupancy() {
        return occupancy;
    }

    public void setOccupancy(String occupancy) {
        this.occupancy = occupancy;
    }

    public Double getHullLimit() {
        return hullLimit;
    }

    public void setHullLimit(Double hullLimit) {
        this.hullLimit = hullLimit;
    }

    public String getPolicyCurrency() {
        return policyCurrency;
    }

    public void setPolicyCurrency(String policyCurrency) {
        this.policyCurrency = policyCurrency;
    }

    public String getPremiumCurrency() {
        return premiumCurrency;
    }

    public void setPremiumCurrency(String premiumCurrency) {
        this.premiumCurrency = premiumCurrency;
    }

    public Double getQuotedWarPremium() {
        return quotedWarPremium;
    }

    public void setQuotedWarPremium(Double quotedWarPremium) {
        this.quotedWarPremium = quotedWarPremium;
    }

    public Double getBoundWarPremium() {
        return boundWarPremium;
    }

    public void setBoundWarPremium(Double boundWarPremium) {
        this.boundWarPremium = boundWarPremium;
    }

    public Double getTlo() {
        return tlo;
    }

    public void setTlo(Double tlo) {
        this.tlo = tlo;
    }

    public Double getHijacking() {
        return hijacking;
    }

    public void setHijacking(Double hijacking) {
        this.hijacking = hijacking;
    }

    public Double getTechnicalRecords() {
        return technicalRecords;
    }

    public void setTechnicalRecords(Double technicalRecords) {
        this.technicalRecords = technicalRecords;
    }

    public Double getMaxHullLimit() {
        return maxHullLimit;
    }

    public void setMaxHullLimit(Double maxHullLimit) {
        this.maxHullLimit = maxHullLimit;
    }

    public Double getAggregateLimit() {
        return aggregateLimit;
    }

    public void setAggregateLimit(Double aggregateLimit) {
        this.aggregateLimit = aggregateLimit;
    }

    public Double getQuotedRate() {
        return quotedRate;
    }

    public void setQuotedRate(Double quotedRate) {
        this.quotedRate = quotedRate;
    }

    public Double getBoundRate() {
        return boundRate;
    }

    public void setBoundRate(Double boundRate) {
        this.boundRate = boundRate;
    }

    public Double getExcess() {
        return excess;
    }

    public void setExcess(Double excess) {
        this.excess = excess;
    }

    public String getHubAirport() {
        return hubAirport;
    }

    public void setHubAirport(String hubAirport) {
        this.hubAirport = hubAirport;
    }

    public Double getTotalFleetValue() {
        return totalFleetValue;
    }

    public void setTotalFleetValue(Double totalFleetValue) {
        this.totalFleetValue = totalFleetValue;
    }

    public Double getAverageFleetValue() {
        return averageFleetValue;
    }

    public void setAverageFleetValue(Double averageFleetValue) {
        this.averageFleetValue = averageFleetValue;
    }

    public Double getAverageAircraftValue() {
        return averageAircraftValue;
    }

    public void setAverageAircraftValue(Double averageAircraftValue) {
        this.averageAircraftValue = averageAircraftValue;
    }
}
