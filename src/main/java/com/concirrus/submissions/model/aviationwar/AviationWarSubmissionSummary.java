package com.concirrus.submissions.model.aviationwar;

import com.concirrus.submissions.model.User;
import com.concirrus.submissions.model.aviation.AviationSubmissionSummary;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.Map;
import java.util.Objects;

@Slf4j
@Data
public class AviationWarSubmissionSummary {
    private String submissionId;
    private  String state;
    private  String status;
    private String coverageType;
    private String lineOfBusiness;
    private String sourceId;
    private String sourceName;
    private AviationWarInsuredInfo aviationWarInsuredInfo;
    private AviationWarBrokerInfo  brokerInfo ;
    private AviationWarRiskDetails riskDetails;
    private User assignedUser ;
    private  String  tCreated ;
    private  String  submissionNumber;
    private  String productType;
    private Boolean isBlocked;
    private String assignedId;



    public static AviationWarSubmissionSummary from(String submissionId, Map<String,Object>submission, ObjectMapper objectMapper){
        if (CollectionUtils.isEmpty(submission)) {
            return new AviationWarSubmissionSummary();
        }
        AviationWarSubmissionSummary submissionSummary = new AviationWarSubmissionSummary();
        submissionSummary.setSubmissionId(submissionId);
        if(!submission.isEmpty() && submission.containsKey("state") && Objects.nonNull(submission.get("state"))){
            submissionSummary.setState(submission.get("state").toString());
        }
        if(!submission.isEmpty() && submission.containsKey("status") && Objects.nonNull(submission.get("status"))){
            submissionSummary.setStatus(submission.get("status").toString());
        }
        if (!submission.isEmpty() && submission.containsKey("insuredInfo") && submission.get("insuredInfo")!=null){
            try {
                AviationWarInsuredInfo insuredInfo = objectMapper.convertValue(submission.get("insuredInfo"),AviationWarInsuredInfo.class);
                submissionSummary.setAviationWarInsuredInfo(insuredInfo);
            } catch (Exception e) {
                log.info("unable to parse aviation war insured info for subId : {}",submissionId);
                submissionSummary.setAviationWarInsuredInfo(null);
            }
        }else {
            log.info(" aviation war insured info is null for subId : {}",submissionId);
        }
        if (!submission.isEmpty() && submission.containsKey("riskDetails") && submission.get("riskDetails")!=null){
            try {
                AviationWarRiskDetails riskDetails = objectMapper.convertValue(submission.get("riskDetails"),AviationWarRiskDetails.class);
                submissionSummary.setRiskDetails(riskDetails);
            } catch (Exception e) {
                submissionSummary.setAviationWarInsuredInfo(null);
            }
        }else {
            log.info(" aviation war risk detail is null for subId : {}",submissionId);
        }
        if (!submission.isEmpty() && submission.containsKey("brokerInfo") && submission.get("brokerInfo")!=null){
            try {
                AviationWarBrokerInfo brokerInfo1 = objectMapper.convertValue(submission.get("brokerInfo"),AviationWarBrokerInfo.class);
                submissionSummary.setBrokerInfo(brokerInfo1);
            } catch (Exception e) {
                submissionSummary.setAviationWarInsuredInfo(null);
            }
        }
        if (!submission.isEmpty() && submission.containsKey("assignedUser") && submission.get("assignedUser")!=null){
            try {
                User assignedUser1 = objectMapper.convertValue(submission.get("assignedUser"),User.class);
                submissionSummary.setAssignedUser(assignedUser1);
            } catch (Exception e) {
                submissionSummary.setAviationWarInsuredInfo(null);
            }
        }
        if (!submission.isEmpty() && submission.containsKey("tcreated")){
            submissionSummary.setTCreated((String) submission.get("tcreated"));
        }
        if (!submission.isEmpty() && submission.containsKey("isBlocked")){
            submissionSummary.setIsBlocked((Boolean) submission.get("isBlocked"));
        }
        if (!submission.isEmpty() && submission.containsKey("submissionNumber")){
            submissionSummary.setSubmissionNumber((String) submission.get("submissionNumber"));
        }
        if (!submission.isEmpty() && submission.containsKey("lineOfBusiness")){
            submissionSummary.setProductType((String) submission.get("lineOfBusiness"));
        }
        if (!submission.isEmpty() && submission.containsKey("assigneeId")){
            submissionSummary.setAssignedId((String) submission.get("assigneeId"));
        }
        if (!submission.isEmpty() && submission.containsKey("lineOfBusiness")){
            submissionSummary.setLineOfBusiness((String) submission.get("lineOfBusiness"));
        }
        if (!submission.isEmpty() && submission.containsKey("coverageType")){
            submissionSummary.setCoverageType((String) submission.get("coverageType"));
        }
        if (!submission.isEmpty() && submission.containsKey("sourceId")){
            submissionSummary.setSourceId((String) submission.get("sourceId"));
        }
        if (!submission.isEmpty() && submission.containsKey("sourceName")){
            submissionSummary.setSourceName((String) submission.get("sourceName"));
        }
        return submissionSummary;
    }
}
