package com.concirrus.submissions.model.construction;

import com.concirrus.submissions.model.InsuredInfo;
import com.concirrus.submissions.model.Producer;
import com.concirrus.submissions.model.User;
import com.concirrus.submissions.model.aviation.AviationSubmissionSummary;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class ConstructionSubmissionSummary {
    private String submissionId;
    private String state;
    private String status;
    private String assigneeId;
    private String blockedBySubmissionId;
    private Boolean isBlocked;
    private List<String> blockedSubmissions;
    private String tCreated;
    private String transactionType;
    private String policyExpirationDate;
    private String policyInceptionDate;
    private String submissionNumber;
    private BrokerInfo brokerInfo;
    private ConstructionInsuredInfo constructionInsuredInfo;
    private String insuredName;
    private String policyType;
    private String priorPolicyNumber;
    private String policyTerm;
    private String brokerage;
    private String broker;
    private String productType;
    private User assignedUser;
    private String policyNumber;
    public String gettCreated() {
        return tCreated;
    }

    public void settCreated(String tCreated) {
        this.tCreated = tCreated;
    }

    public String getPolicyNumber() {
        return policyNumber;
    }

    public void setPolicyNumber(String policyNumber) {
        this.policyNumber = policyNumber;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public String getPolicyExpirationDate() {
        return policyExpirationDate;
    }

    public void setPolicyExpirationDate(String policyExpirationDate) {
        this.policyExpirationDate = policyExpirationDate;
    }

    public String getPolicyInceptionDate() {
        return policyInceptionDate;
    }

    public void setPolicyInceptionDate(String policyInceptionDate) {
        this.policyInceptionDate = policyInceptionDate;
    }

    public String getSubmissionNumber() {
        return submissionNumber;
    }

    public void setSubmissionNumber(String submissionNumber) {
        this.submissionNumber = submissionNumber;
    }

    public BrokerInfo getBrokerInfo() {
        return brokerInfo;
    }

    public void setBrokerInfo(BrokerInfo brokerInfo) {
        this.brokerInfo = brokerInfo;
    }

    public ConstructionInsuredInfo getConstructionInsuredInfo() {
        return constructionInsuredInfo;
    }

    public void setConstructionInsuredInfo(ConstructionInsuredInfo constructionInsuredInfo) {
        this.constructionInsuredInfo = constructionInsuredInfo;
    }

    public String getPriorPolicyNumber() {
        return priorPolicyNumber;
    }

    public void setPriorPolicyNumber(String priorPolicyNumber) {
        this.priorPolicyNumber = priorPolicyNumber;
    }

    public String getSubmissionId() {
        return submissionId;
    }

    public void setSubmissionId(String submissionId) {
        this.submissionId = submissionId;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAssigneeId() {
        return assigneeId;
    }

    public void setAssigneeId(String assigneeId) {
        this.assigneeId = assigneeId;
    }

    public String getBlockedBySubmissionId() { return blockedBySubmissionId;}

    public void setBlockedBySubmissionId(String blockedBySubmissionId) { this.blockedBySubmissionId = blockedBySubmissionId;}

    public Boolean getBlocked() { return isBlocked;}

    public void setBlocked(Boolean blocked) { isBlocked = blocked;}

    public List<String> getBlockedSubmissions() { return blockedSubmissions;}

    public void setBlockedSubmissions(List<String> blockedSubmissions) {this.blockedSubmissions = blockedSubmissions;}

    public String getInsuredName() {
        return insuredName;
    }

    public void setInsuredName(String insuredName) {
        this.insuredName = insuredName;
    }

    public String getPolicyType() {
        return policyType;
    }

    public void setPolicyType(String policyType) {
        this.policyType = policyType;
    }

    public String getPolicyTerm() {
        return policyTerm;
    }

    public void setPolicyTerm(String policyTerm) {
        this.policyTerm = policyTerm;
    }

    public String getBrokerage() {
        return brokerage;
    }

    public void setBrokerage(String brokerage) {
        this.brokerage = brokerage;
    }

    public String getBroker() {
        return broker;
    }

    public void setBroker(String broker) {
        this.broker = broker;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public User getAssignedUser() {
        return assignedUser;
    }

    public void setAssignedUser(User assignedUser) {
        this.assignedUser = assignedUser;
    }

    public static ConstructionSubmissionSummary from(Map<String, Object> submission, String submissionId) {
        if (CollectionUtils.isEmpty(submission)) {
            return null;
        }
        ConstructionSubmissionSummary submissionSummary = new ConstructionSubmissionSummary();
        submissionSummary.setSubmissionId(submissionId);
        if(!submission.isEmpty() && submission.containsKey("state") && Objects.nonNull(submission.get("state"))){
            submissionSummary.setState(submission.get("state").toString());
        }
        if(!submission.isEmpty() && submission.containsKey("status") && Objects.nonNull(submission.get("status"))){
            submissionSummary.setStatus(submission.get("status").toString());
        }
        if(!submission.isEmpty() && submission.containsKey("assigneeId") && Objects.nonNull(submission.get("assigneeId"))){
            submissionSummary.setAssigneeId(submission.get("assigneeId").toString());
        }
        if(!submission.isEmpty() && submission.containsKey("insuredName") && Objects.nonNull(submission.get("insuredName"))){
            submissionSummary.setInsuredName(submission.get("insuredName").toString());
        }
        if(!submission.isEmpty() && submission.containsKey("insuredName") && Objects.nonNull(submission.get("insuredName"))){
            submissionSummary.setInsuredName(submission.get("insuredName").toString());
        }

        if(!submission.isEmpty() && submission.containsKey("insuredName") && Objects.nonNull(submission.get("insuredName"))){
            submissionSummary.setInsuredName(submission.get("insuredName").toString());
        }

        if(!submission.isEmpty() && submission.containsKey("policyNumber") && Objects.nonNull(submission.get("policyNumber"))){
            submissionSummary.setPolicyNumber(submission.get("policyNumber").toString());
        }

        if(!submission.isEmpty() && submission.containsKey("policyTerm") && Objects.nonNull(submission.get("policyTerm"))){
            submissionSummary.setPolicyTerm(submission.get("policyTerm").toString());
        }

        if(!submission.isEmpty() && submission.containsKey("transactionType") && Objects.nonNull(submission.get("transactionType"))){
            submissionSummary.setPolicyType(submission.get("transactionType").toString());
            submissionSummary.setTransactionType(submission.get("transactionType").toString());
        }

        if(!submission.isEmpty() && submission.containsKey("productType") && Objects.nonNull(submission.get("productType"))){
            submissionSummary.setProductType(submission.get("productType").toString());
        }

        if(!submission.isEmpty() && submission.containsKey("tcreated") && Objects.nonNull(submission.get("tcreated"))){
            submissionSummary.settCreated(submission.get("tcreated").toString());
        }

        if(!submission.isEmpty() && submission.containsKey("submissionNumber") && Objects.nonNull(submission.get("submissionNumber"))){
            submissionSummary.setSubmissionNumber(submission.get("submissionNumber").toString());
        }

        if(!submission.isEmpty() && submission.containsKey("policyInceptionDate") && Objects.nonNull(submission.get("policyInceptionDate"))){
            submissionSummary.setPolicyInceptionDate(submission.get("policyInceptionDate").toString());
        }

        if(!submission.isEmpty() && submission.containsKey("policyExpirationDate") && Objects.nonNull(submission.get("policyExpirationDate"))){
            submissionSummary.setPolicyExpirationDate(submission.get("policyExpirationDate").toString());
        }

        if(!submission.isEmpty() && submission.containsKey("priorPolicyNumber") && Objects.nonNull(submission.get("priorPolicyNumber"))){
            submissionSummary.setPriorPolicyNumber(submission.get("priorPolicyNumber").toString());
        }

        if(!submission.isEmpty()) {
            if(submission.containsKey("blockedBySubmissionId") && Objects.nonNull(submission.get("blockedBySubmissionId"))) {
                submissionSummary.setBlockedBySubmissionId(submission.get("blockedBySubmissionId").toString());
            }
            if(submission.containsKey("isBlocked") && Objects.nonNull(submission.get("isBlocked"))) {
                submissionSummary.setBlocked((Boolean) submission.get("isBlocked"));
            }
            if (submission.containsKey("blockedSubmissions") && Objects.nonNull(submission.get("blockedSubmissions"))) {
                Object blockedSubmissionsObject = submission.get("blockedSubmissions");
                if (blockedSubmissionsObject instanceof List<?>) {
                    List<String> blockedSubmissionsList = (List<String>) blockedSubmissionsObject;
                    submissionSummary.setBlockedSubmissions(blockedSubmissionsList);
                }
            }
            if (submission.containsKey("brokerInfo") && Objects.nonNull(submission.get("brokerInfo"))) {
                Map<String, Object> brokerInfoObj = (Map<String, Object>)submission.get("brokerInfo");
                submissionSummary.setBroker(getString(brokerInfoObj, "senderName"));
                submissionSummary.setBrokerage(getString(brokerInfoObj, "name"));
                Object brokerMailingAddress = brokerInfoObj.get("mailingAddress");
                submissionSummary.setBrokerInfo(new BrokerInfo(getString(brokerInfoObj, "senderName"),getString(brokerInfoObj, "name"),brokerMailingAddress));
            }

            if (submission.containsKey("insuredInfo") && Objects.nonNull(submission.get("insuredInfo"))) {
                Map<String, Object> insuredInfoObj = (Map<String, Object>)submission.get("insuredInfo");
                List<String> otherInsured = new ArrayList<>();
                Object otherInsuredObject = insuredInfoObj.get("otherNamedInsureds");
                if (otherInsuredObject instanceof List<?>) {
                    otherInsured = (List<String>) otherInsuredObject;
                }
                String owner =  getString(insuredInfoObj, "owner");
                String firstName =  getString(insuredInfoObj, "firstName");
                Object mailingAddressObject = insuredInfoObj.get("mailingAddress");
                submissionSummary.setConstructionInsuredInfo(new ConstructionInsuredInfo(otherInsured, owner, firstName,mailingAddressObject));
            }
        }
        return submissionSummary;
    }

    private static String getString(Map<String, Object> map, String key) {
        if (map.containsKey(key) && Objects.nonNull(map.get(key))) {
            return map.get(key).toString();
        }
        return null;
    }

}
