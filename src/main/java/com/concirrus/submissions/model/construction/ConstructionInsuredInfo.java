package com.concirrus.submissions.model.construction;

import java.util.List;

public class ConstructionInsuredInfo {

    private List<String> otherNamedInsureds;

    private String owner;

    private String firstName;

    private Object mailingAddress;


    public ConstructionInsuredInfo() {
    }


    public ConstructionInsuredInfo(List<String> otherNamedInsureds, String owner,String firstName,Object mailingAddress) {
        this.otherNamedInsureds = otherNamedInsureds;
        this.owner = owner;
        this.firstName = firstName;
        this.mailingAddress = mailingAddress;
    }

    public List<String> getOtherNameInsured() {
        return otherNamedInsureds;
    }

    public void setOtherNameInsured(List<String> otherNameInsured) {
        this.otherNamedInsureds = otherNameInsured;
    }

    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }


    public List<String> getOtherNamedInsureds() {
        return otherNamedInsureds;
    }

    public void setOtherNamedInsureds(List<String> otherNamedInsureds) {
        this.otherNamedInsureds = otherNamedInsureds;
    }

    public Object getMailingAddress() {
        return mailingAddress;
    }

    public void setMailingAddress(Object mailingAddress) {
        this.mailingAddress = mailingAddress;
    }
}
