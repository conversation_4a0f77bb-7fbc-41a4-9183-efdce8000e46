package com.concirrus.submissions.integration;

import com.amazonaws.util.CollectionUtils;
import com.concirrus.quest.common.rest.model.BasicResponse;
import com.concirrus.submission.connector.accessmanagement.AccessManagementConnector;
import com.concirrus.submission.connector.accessmanagement.model.ClientConfigResponse;
import com.concirrus.submission.connector.accessmanagement.model.TokenResponse;
import com.concirrus.submission.connector.accessmanagement.model.UserInfoResponse;
import com.concirrus.submission.connector.submissionmanager.model.SubmissionState;
import com.concirrus.submission.connector.submissionmanager.model.UpdateType;
import com.concirrus.submissions.model.User;
import com.concirrus.submissions.model.aviation.AviationSubmissionSummary;
import com.concirrus.submissions.model.aviation.AviationSubmissionsChange;
import com.concirrus.submissionservice.connector.submissionhandler.SubmissionHandlerConnector;
import com.concirrus.submissionservice.connector.submissionhandler.model.AviationSubmissionChangeEventDto;
import com.concirrus.submissionservice.connector.submissionhandler.model.ProductType;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jetbrains.annotations.Nullable;
import org.reactivestreams.Publisher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import reactor.core.publisher.Flux;
import reactor.core.publisher.UnicastProcessor;

import javax.annotation.PostConstruct;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Validated
@Service
public class AviationSubmissionChangesService {

    private final SubmissionHandlerConnector submissionHandlerConnector;

    private final AccessManagementConnector accessManagementConnector;

    private final ObjectMapper objectMapper;

    public AviationSubmissionChangesService(final SubmissionHandlerConnector submissionHandlerConnector, final AccessManagementConnector accessManagementConnector, ObjectMapper objectMapper) {
        this.submissionHandlerConnector = submissionHandlerConnector;
        this.accessManagementConnector = accessManagementConnector;
        this.objectMapper = objectMapper;
    }

    private static final Logger LOG = LoggerFactory.getLogger(AviationSubmissionChangesService.class);

    private final Map<String,ClientConfigResponse> clientConfigResponseMap = new HashMap<>();
    private final Map<String, Flux<Long>> clientSubmissionsInboxItemCount = new HashMap<>();
    private final Map<String, Flux<Long>> clientSubmissionsInReviewItemCount = new HashMap<>();
    private final Map<String, Flux<Long>> clientSubmissionsQuotedItemCount = new HashMap<>();
    private final Map<String, Flux<Long>> clientSubmissionsDoneItemCount = new HashMap<>();
    private final Map<String,Flux<AviationSubmissionsChange>> clientSubmissionStateChange = new HashMap<>();
    private final PublisherContainer<AviationSubmissionChangeEventDto> submissionChangesPublisherContainer = new PublisherContainer<>();

    @PostConstruct
    void init() {
        initClientConfigMap();
        initSubmissionFluxes();
        initSubmissionChanges();
    }

    private void initClientConfigMap() {
        BasicResponse<List<ClientConfigResponse>> clientsConfig = accessManagementConnector.getClientsConfig();
        if(HttpStatus.OK.value()==clientsConfig.getStatus()&& !CollectionUtils.isNullOrEmpty(clientsConfig.getResult()))
        {
            for(ClientConfigResponse clientConfigResponse:clientsConfig.getResult())
            {
                clientConfigResponseMap.put(clientConfigResponse.getId(),clientConfigResponse);
            }
        }
    }

    private void initSubmissionFluxes() {
        for (String clientId : clientConfigResponseMap.keySet()) {
            Flux<Long> submissionsInboxItemCount = submissionChangesPublisherContainer.getFlux()
                    .filter(this::submissionStateChanged)
                    .map(submissionChangesEvent -> getTotalSubmissionCountByState(clientId, SubmissionState.INBOX, Boolean.FALSE, submissionChangesEvent.getType()))
                    .publish()
                    .autoConnect();
            clientSubmissionsInboxItemCount.put(clientId, submissionsInboxItemCount);

            Flux<Long> submissionsInReviewItemCount = submissionChangesPublisherContainer.getFlux()
                    .filter(this::submissionStateChanged)
                    .map(submissionChangesEvent -> getTotalSubmissionCountByState(clientId, SubmissionState.IN_REVIEW, Boolean.FALSE, submissionChangesEvent.getType()))
                    .publish()
                    .autoConnect();
            clientSubmissionsInReviewItemCount.put(clientId, submissionsInReviewItemCount);

            Flux<Long> submissionsQuotedItemCount = submissionChangesPublisherContainer.getFlux()
                    .filter(this::submissionStateChanged)
                    .map(submissionChangesEvent -> getTotalSubmissionCountByState(clientId, SubmissionState.QUOTED, Boolean.FALSE, submissionChangesEvent.getType()))
                    .publish()
                    .autoConnect();
            clientSubmissionsQuotedItemCount.put(clientId, submissionsQuotedItemCount);


            Flux<Long> submissionsDoneItemCount = submissionChangesPublisherContainer.getFlux()
                    .filter(this::submissionStateChanged)
                    .map(submissionChangesEvent -> getTotalSubmissionCountByState(clientId, SubmissionState.DONE, Boolean.FALSE, submissionChangesEvent.getType()))
                    .publish()
                    .autoConnect();
            clientSubmissionsDoneItemCount.put(clientId, submissionsDoneItemCount);
        }
    }

    private void initSubmissionChanges() {
        for(String clientId : clientConfigResponseMap.keySet())
        {
            Flux<AviationSubmissionsChange> submissionsBoardChangeFlux=  submissionChangesPublisherContainer.getFlux()
                    .map(this::getSubmissionBoardStateChanges)
                    .publish()
                    .autoConnect();
            clientSubmissionStateChange.put(clientId,submissionsBoardChangeFlux);
        }
    }

    private AviationSubmissionsChange getSubmissionBoardStateChanges(AviationSubmissionChangeEventDto submissionChangesEvent)  {
        try {
            LOG.info("Get Submission board changes for submission id : {}", submissionChangesEvent.getSubmissionId());
            Map<String, Object> submission = getSubmission(submissionChangesEvent);
            AviationSubmissionSummary updatedSubmission = AviationSubmissionSummary.from(submission, submissionChangesEvent.getSubmissionId());

            // Populating assigned user details
            populateAssignedUserDetail(submissionChangesEvent, updatedSubmission);

            AviationSubmissionSummary previousSubmission = AviationSubmissionSummary.from(submissionChangesEvent.getPreviousSubmission(), submissionChangesEvent.getSubmissionId());

            // Populating assigned user details
            populateAssignedUserDetail(submissionChangesEvent, previousSubmission);

            AviationSubmissionsChange submissionsChange = new AviationSubmissionsChange(submissionChangesEvent.getSubmissionId(), submissionChangesEvent.getUpdateType().toString(), updatedSubmission, previousSubmission);
            LOG.info("Send Submission change event for submission id : {} submission : {}", submissionsChange.getSubmissionId(), objectMapper.writeValueAsString(submissionsChange));
            return submissionsChange;
        }
        catch (Exception e){
            LOG.error("Error while getSubmissionBoardStateChanges : {}", e);
        }
        return null;
    }

    private void populateAssignedUserDetail(AviationSubmissionChangeEventDto submissionChangesEvent, AviationSubmissionSummary updatedSubmission) {
        UserInfoResponse userInfo = null;
        if (Objects.nonNull(updatedSubmission) && StringUtils.hasText(updatedSubmission.getAssigneeId())) {
            BasicResponse<TokenResponse> tokenResponse = accessManagementConnector.getToken(submissionChangesEvent.getClientId());
            if (HttpStatus.OK.value() == tokenResponse.getStatus() && tokenResponse.getResult() != null) {
                BasicResponse<UserInfoResponse> userInfoBasicResponse = accessManagementConnector.getUserById(updatedSubmission.getAssigneeId(),submissionChangesEvent.getClientId(), tokenResponse.getResult().getAccessToken());
                userInfo = HttpStatus.OK.value() == userInfoBasicResponse.getStatus() ? userInfoBasicResponse.getResult() : null;
            } else {
                LOG.error("Error while fetching token for clientId {} due to {}", submissionChangesEvent.getClientId(), tokenResponse.getError());
            }
            User user = User.getInstance(userInfo);
            updatedSubmission.setAssignedUser(user);
        }
    }

    @Nullable
    private Map<String, Object> getSubmission(AviationSubmissionChangeEventDto submissionChangesEvent) {
        BasicResponse<Object> submissionBasicResponse = submissionHandlerConnector.getSubmissionById(submissionChangesEvent.getSubmissionId(), submissionChangesEvent.getClientId(),true);
        if(submissionBasicResponse!=null && HttpStatus.OK.value()==submissionBasicResponse.getStatus() && submissionBasicResponse.getResult()!=null)
        {
           return objectMapper.convertValue(submissionBasicResponse.getResult(), Map.class);
        }
        LOG.info("Submission not found");
        return null;
    }

    private Long getTotalSubmissionCountByState(String clientId, SubmissionState submissionState, Boolean showArchived, ProductType type) {
        if(!clientConfigResponseMap.containsKey(clientId))
        {
            initClientConfigMap();
        }
        com.concirrus.submissionservice.connector.submissionhandler.model.SubmissionState state = com.concirrus.submissionservice.connector.submissionhandler.model.SubmissionState.valueOf(submissionState.name());
        BasicResponse<Long> response = submissionHandlerConnector.getSubmissionCountByState(clientId, state, showArchived, type);
        if (HttpStatus.OK.value() == response.getStatus()) {
            return response.getResult();
        }
        return 0L;
    }

    private boolean submissionStateChanged(AviationSubmissionChangeEventDto submissionChangesEvent) {
        Map<String,Object> submission =  getSubmission(submissionChangesEvent);
        if(!UpdateType.CREATED.equals(submissionChangesEvent.getUpdateType())  && submissionChangesEvent.getPreviousSubmission() !=null && submission!=null)
        {
         return ! submission.get("state").equals(submissionChangesEvent.getPreviousSubmission().get("state"));
        }
        else {
            return true;
        }
    }

    public Publisher<Long> getSubmissionItemCountPublisher(String clientId, String submissionState, ProductType type) {

        if (SubmissionState.INBOX.name().equals(submissionState)) {
            return clientSubmissionsInboxItemCount.get(clientId);
        } else if (SubmissionState.IN_REVIEW.name().equals(submissionState)) {
            return clientSubmissionsInReviewItemCount.get(clientId);
        } else if (SubmissionState.QUOTED.name().equals(submissionState)) {
            return clientSubmissionsQuotedItemCount.get(clientId);
        } else {
            return clientSubmissionsDoneItemCount.get(clientId);
        }
    }

    public void notifySubscribersAboutChanges(@Valid AviationSubmissionChangeEventDto submissionChangesEvent) {
        submissionChangesPublisherContainer.onNext(submissionChangesEvent);
    }

    public Publisher<AviationSubmissionsChange> getSubmissionChanges(String clientId) {
       return clientSubmissionStateChange.get(clientId);
    }


    private static class PublisherContainer<T> {
        private final UnicastProcessor<T> processor = UnicastProcessor.create();
        private final Flux<T> flux = processor.publish().autoConnect();

        public PublisherContainer() {
        }

        public UnicastProcessor<T> getProcessor() {
            return processor;
        }

        public Flux<T> getFlux() {
            return flux;
        }

        void onNext(T t) {
            processor.onNext(t);
        }
    }

}
