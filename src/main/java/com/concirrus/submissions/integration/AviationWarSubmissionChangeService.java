package com.concirrus.submissions.integration;

import com.amazonaws.util.CollectionUtils;
import com.concirrus.quest.common.rest.model.BasicResponse;
import com.concirrus.submission.connector.accessmanagement.AccessManagementConnector;
import com.concirrus.submission.connector.accessmanagement.model.ClientConfigResponse;
import com.concirrus.submission.connector.accessmanagement.model.TokenResponse;
import com.concirrus.submission.connector.accessmanagement.model.UserInfoResponse;
import com.concirrus.submission.connector.submissionmanager.model.SubmissionState;
import com.concirrus.submissions.model.aviationwar.AviationWarJobUpdate;
import com.concirrus.submissions.model.aviationwar.AviationWarSubmissionSummary;
import com.concirrus.submissions.websocket.EventWebSocketHandler;
import com.concirrus.submissionservice.connector.submissionhandler.model.UpdateType;
import com.concirrus.submissions.model.User;
import com.concirrus.submissions.model.aviationwar.AviationWarSubmissionChange;
import com.concirrus.submissionservice.connector.submissionhandler.SubmissionHandlerConnector;
import com.concirrus.submissionservice.connector.submissionhandler.model.ProductType;
import com.concirrus.submissionservice.connector.submissionhandler.model.SubmissionChangeEventDto;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jetbrains.annotations.Nullable;
import org.reactivestreams.Publisher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import reactor.core.publisher.Flux;
import reactor.core.publisher.UnicastProcessor;

import javax.annotation.PostConstruct;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Validated
@Service
public class AviationWarSubmissionChangeService {
    private final SubmissionHandlerConnector submissionHandlerConnector;

    private final AccessManagementConnector accessManagementConnector;

    private final ObjectMapper objectMapper;
    private static Logger logger = LoggerFactory.getLogger(AviationWarSubmissionChangeService.class);

    private final Map<String,ClientConfigResponse> clientConfigResponseMap = new HashMap<>();
    private final Map<String, Flux<Long>> clientSubmissionsInboxItemCount = new HashMap<>();
    private final Map<String, Flux<Long>> clientSubmissionsInReviewItemCount = new HashMap<>();
    private final Map<String, Flux<Long>> clientSubmissionsQuotedItemCount = new HashMap<>();
    private final Map<String, Flux<Long>> clientSubmissionsDoneItemCount = new HashMap<>();
    private final PublisherContainer<SubmissionChangeEventDto> submissionChangesPublisherContainer = new PublisherContainer<>();
    private final Map<String,Flux<AviationWarSubmissionChange>> clientSubmissionStateChange = new HashMap<>();
    private final EventWebSocketHandler eventWebSocketHandler;

    public AviationWarSubmissionChangeService(SubmissionHandlerConnector submissionHandlerConnector, AccessManagementConnector accessManagementConnector, ObjectMapper objectMapper, EventWebSocketHandler eventWebSocketHandler) {
        this.submissionHandlerConnector = submissionHandlerConnector;
        this.accessManagementConnector = accessManagementConnector;
        this.objectMapper = objectMapper;
        this.eventWebSocketHandler = eventWebSocketHandler;
    }

    @PostConstruct
    void init() {
        initClientConfigMap();
        initSubmissionFluxes();
        initSubmissionChanges();
    }

    private void initClientConfigMap() {
        BasicResponse<List<ClientConfigResponse>> clientsConfig = accessManagementConnector.getClientsConfig();
        if(HttpStatus.OK.value()==clientsConfig.getStatus()&& !CollectionUtils.isNullOrEmpty(clientsConfig.getResult()))
        {
            for(ClientConfigResponse clientConfigResponse:clientsConfig.getResult())
            {
                clientConfigResponseMap.put(clientConfigResponse.getId(),clientConfigResponse);
            }
        }
    }

    private void initSubmissionFluxes() {
        for (String clientId : clientConfigResponseMap.keySet()) {
            Flux<Long> submissionsInboxItemCount = submissionChangesPublisherContainer.getFlux()
                    .filter(this::submissionStateChanged)
                    .map(submissionChangesEvent -> getTotalSubmissionCountByState(clientId, SubmissionState.INBOX, Boolean.FALSE, submissionChangesEvent.getType()))
                    .publish()
                    .autoConnect();
            clientSubmissionsInboxItemCount.put(clientId, submissionsInboxItemCount);

            Flux<Long> submissionsInReviewItemCount = submissionChangesPublisherContainer.getFlux()
                    .filter(this::submissionStateChanged)
                    .map(submissionChangesEvent -> getTotalSubmissionCountByState(clientId, SubmissionState.IN_REVIEW, Boolean.FALSE, submissionChangesEvent.getType()))
                    .publish()
                    .autoConnect();
            clientSubmissionsInReviewItemCount.put(clientId, submissionsInReviewItemCount);

            Flux<Long> submissionsQuotedItemCount = submissionChangesPublisherContainer.getFlux()
                    .filter(this::submissionStateChanged)
                    .map(submissionChangesEvent -> getTotalSubmissionCountByState(clientId, SubmissionState.QUOTED, Boolean.FALSE, submissionChangesEvent.getType()))
                    .publish()
                    .autoConnect();
            clientSubmissionsQuotedItemCount.put(clientId, submissionsQuotedItemCount);


            Flux<Long> submissionsDoneItemCount = submissionChangesPublisherContainer.getFlux()
                    .filter(this::submissionStateChanged)
                    .map(submissionChangesEvent -> getTotalSubmissionCountByState(clientId, SubmissionState.DONE, Boolean.FALSE, submissionChangesEvent.getType()))
                    .publish()
                    .autoConnect();
            clientSubmissionsDoneItemCount.put(clientId, submissionsDoneItemCount);
        }
    }

    private void initSubmissionChanges() {
        for(String clientId : clientConfigResponseMap.keySet())
        {
            Flux<AviationWarSubmissionChange> submissionsBoardChangeFlux=  submissionChangesPublisherContainer.getFlux()
                    .map(this::getSubmissionBoardStateChanges)
                    .publish()
                    .autoConnect();
            clientSubmissionStateChange.put(clientId,submissionsBoardChangeFlux);
        }
    }
    private boolean submissionStateChanged(SubmissionChangeEventDto submissionChangesEvent) {
        Map<String,Object> submission =  getSubmission(submissionChangesEvent);
        if(!Objects.equals(UpdateType.CREATED,submissionChangesEvent.getUpdateType())  && submissionChangesEvent.getPreviousSubmission() !=null && submission!=null)
        {
            return ! submission.get("state").equals(submissionChangesEvent.getPreviousSubmission().get("state"));
        }
        else {
            return true;
        }
    }
    @Nullable
    private Map<String, Object> getSubmission(SubmissionChangeEventDto submissionChangesEvent) {
        BasicResponse<Object> submissionBasicResponse = submissionHandlerConnector.getSubmissionById(submissionChangesEvent.getSubmissionId(), submissionChangesEvent.getClientId(),true);
        if(submissionBasicResponse!=null && HttpStatus.OK.value()==submissionBasicResponse.getStatus() && submissionBasicResponse.getResult()!=null)
        {
            return objectMapper.convertValue(submissionBasicResponse.getResult(), Map.class);
        }
        logger.info("Submission not found");
        return null;
    }
    private Long getTotalSubmissionCountByState(String clientId, SubmissionState submissionState, Boolean showArchived, ProductType type) {
        if(!clientConfigResponseMap.containsKey(clientId))
        {
            initClientConfigMap();
        }
        com.concirrus.submissionservice.connector.submissionhandler.model.SubmissionState state = com.concirrus.submissionservice.connector.submissionhandler.model.SubmissionState.valueOf(submissionState.name());
        BasicResponse<Long> response = submissionHandlerConnector.getSubmissionCountByState(clientId, state, showArchived, type);
        if (HttpStatus.OK.value() == response.getStatus()) {
            return response.getResult();
        }
        return 0L;
    }

    private AviationWarSubmissionChange getSubmissionBoardStateChanges(SubmissionChangeEventDto submissionChangesEvent)  {
        try {
            logger.info("Get Submission board changes for submission id : {}", submissionChangesEvent.getSubmissionId());
            AviationWarSubmissionSummary updatedSubmission = AviationWarSubmissionSummary.from(submissionChangesEvent.getSubmissionId(),getSubmission(submissionChangesEvent),objectMapper);

            // Populating assigned user details
            populateAssignedUserDetail(submissionChangesEvent, updatedSubmission);

            AviationWarSubmissionSummary previousSubmission = AviationWarSubmissionSummary.from(submissionChangesEvent.getSubmissionId(),submissionChangesEvent.getPreviousSubmission(),objectMapper);

            // Populating assigned user details
            populateAssignedUserDetail(submissionChangesEvent, previousSubmission);

            AviationWarSubmissionChange submissionsChange = new AviationWarSubmissionChange(submissionChangesEvent.getSubmissionId(), submissionChangesEvent.getUpdateType().toString(), updatedSubmission, previousSubmission);
            logger.info("Send Submission change event for submission id : {} submission : {}", submissionsChange.getSubmissionId(), objectMapper.writeValueAsString(submissionsChange));
            return submissionsChange;
        }
        catch (Exception e){
            logger.error("Error while getSubmissionBoardStateChanges : {}", e.getMessage());
        }
        return null;
    }

    private AviationWarJobUpdate getJobUpdate(SubmissionChangeEventDto submissionChangeEventDto){
        if (submissionChangeEventDto.getUpdateType().equals(UpdateType.JOB_UPDATE)) {
            Map<String, Object> submission = submissionChangeEventDto.getPreviousSubmission();
            String jobId = (String) submission.get("currentJob");
            String jobStatus = (String) submission.get("currentJobStatus");
            String submissionId = submissionChangeEventDto.getSubmissionId();
            AviationWarJobUpdate jobUpdate = new AviationWarJobUpdate();
            jobUpdate.setJobId(jobId);
            jobUpdate.setSubmissionId(submissionId);
            jobUpdate.setJobStatus(jobStatus);
            Map<String,Object>riskDetails = submission.get("riskDetails")!=null? (Map<String, Object>) submission.get("riskDetails") :new HashMap<>();
            jobUpdate.setNumberOfLocations((Integer) riskDetails.get("numberOfLocations"));
            jobUpdate.setCalculatedTiv((Double) submission.get("calculatedTiv"));
            return jobUpdate;
        }
        return null;
    }

    private void populateAssignedUserDetail(SubmissionChangeEventDto submissionChangesEvent,AviationWarSubmissionSummary updatedSubmission) {
        UserInfoResponse userInfo = null;
        String assigneeId = StringUtils.hasText(updatedSubmission.getAssignedId())?updatedSubmission.getAssignedId():null;
        if (StringUtils.hasText(assigneeId)) {
            BasicResponse<TokenResponse> tokenResponse = accessManagementConnector.getToken(submissionChangesEvent.getClientId());
            if (HttpStatus.OK.value() == tokenResponse.getStatus() && tokenResponse.getResult() != null) {
                BasicResponse<UserInfoResponse> userInfoBasicResponse = accessManagementConnector.getUserById(assigneeId,submissionChangesEvent.getClientId(), tokenResponse.getResult().getAccessToken());
                userInfo = HttpStatus.OK.value() == userInfoBasicResponse.getStatus() ? userInfoBasicResponse.getResult() : null;
            } else {
                logger.error("Error while fetching token for clientId {} due to {}", submissionChangesEvent.getClientId(), tokenResponse.getError());
            }
            User user = User.getInstance(userInfo);
            updatedSubmission.setAssignedUser(user);
        }
    }
    public void notifySubscribersAboutChanges(@Valid SubmissionChangeEventDto submissionChangesEvent) {
        logger.info("Receive message {}", "test1");
        submissionChangesPublisherContainer.onNext(submissionChangesEvent);
        logger.info("Receive message {}", "test2");
    }
    public Publisher<AviationWarSubmissionChange> getSubmissionChanges(String clientId) {
        return clientSubmissionStateChange.get(clientId);
    }

    public Publisher<Long> getSubmissionItemCountPublisher(String clientId, String submissionState, ProductType type) {

        if (SubmissionState.INBOX.name().equals(submissionState)) {
            return clientSubmissionsInboxItemCount.get(clientId);
        } else if (SubmissionState.IN_REVIEW.name().equals(submissionState)) {
            return clientSubmissionsInReviewItemCount.get(clientId);
        } else if (SubmissionState.QUOTED.name().equals(submissionState)) {
            return clientSubmissionsQuotedItemCount.get(clientId);
        } else {
            return clientSubmissionsDoneItemCount.get(clientId);
        }
    }

    public void sendWebsocketEvents(SubmissionChangeEventDto changeEventDto) throws JsonProcessingException {
        eventWebSocketHandler.sendMessageToClient(changeEventDto.getClientId(),objectMapper.writeValueAsString(getJobUpdate(changeEventDto)));
    }

    private static class PublisherContainer<T> {
        private final UnicastProcessor<T> processor = UnicastProcessor.create();
        private final Flux<T> flux = processor.publish().autoConnect();

        public PublisherContainer() {
        }

        public UnicastProcessor<T> getProcessor() {
            return processor;
        }

        public Flux<T> getFlux() {
            return flux;
        }

        void onNext(T t) {
            processor.onNext(t);
        }
    }

}
