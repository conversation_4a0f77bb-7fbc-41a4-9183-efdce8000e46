package com.concirrus.submissions.integration;

import com.amazonaws.util.CollectionUtils;
import com.concirrus.quest.common.rest.model.BasicResponse;
import com.concirrus.submission.connector.accessmanagement.AccessManagementConnector;
import com.concirrus.submission.connector.accessmanagement.model.ClientConfigResponse;
import com.concirrus.submission.connector.accessmanagement.model.TokenResponse;
import com.concirrus.submission.connector.submissionmanager.SubmissionManagerConnector;
import com.concirrus.submission.connector.submissionmanager.model.Submission;
import com.concirrus.submission.connector.submissionmanager.model.SubmissionChangeEventDto;
import com.concirrus.submission.connector.submissionmanager.model.SubmissionState;
import com.concirrus.submission.connector.submissionmanager.model.UpdateType;
import com.concirrus.submissions.connector.questinsights.QuestInsightsConnector;
import com.concirrus.submissions.connector.questinsights.model.user.UserInfo;
import com.concirrus.submissions.model.SubmissionDetail;
import com.concirrus.submissions.model.SubmissionSummary;
import com.concirrus.submissions.model.SubmissionsChange;
import com.concirrus.submissions.model.User;
import org.jetbrains.annotations.Nullable;
import org.reactivestreams.Publisher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import reactor.core.publisher.Flux;
import reactor.core.publisher.UnicastProcessor;

import javax.annotation.PostConstruct;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Validated
@Service
public class SubmissionChangesService {

    private final SubmissionManagerConnector submissionManagerConnector;

    private final AccessManagementConnector accessManagementConnector;

    private final QuestInsightsConnector questInsightsConnector;

    public SubmissionChangesService(final SubmissionManagerConnector submissionManagerConnector,final AccessManagementConnector accessManagementConnector,final QuestInsightsConnector questInsightsConnector) {
        this.submissionManagerConnector = submissionManagerConnector;
        this.accessManagementConnector = accessManagementConnector;
        this.questInsightsConnector = questInsightsConnector;
    }

    private static final Logger LOG = LoggerFactory.getLogger(SubmissionChangesService.class);

    private final Map<String,ClientConfigResponse> clientConfigResponseMap = new HashMap<>();
    private final Map<String, Flux<Long>> clientSubmissionsInboxItemCount = new HashMap<>();
    private final Map<String, Flux<Long>> clientSubmissionsInReviewItemCount = new HashMap<>();
    private final Map<String, Flux<Long>> clientSubmissionsQuotedItemCount = new HashMap<>();
    private final Map<String, Flux<Long>> clientSubmissionsDoneItemCount = new HashMap<>();
    private final Map<String,Flux<SubmissionsChange>> clientSubmissionStateChange = new HashMap<>();
    private final PublisherContainer<SubmissionChangeEventDto> submissionChangesPublisherContainer = new PublisherContainer<>();

    @PostConstruct
    void init() {
        initClientConfigMap();
        initSubmissionFluxes();
        initSubmissionChanges();
    }

    private void initClientConfigMap() {
        BasicResponse<List<ClientConfigResponse>> clientsConfig = accessManagementConnector.getClientsConfig();
        if(HttpStatus.OK.value()==clientsConfig.getStatus()&& !CollectionUtils.isNullOrEmpty(clientsConfig.getResult()))
        {
            for(ClientConfigResponse clientConfigResponse:clientsConfig.getResult())
            {
                clientConfigResponseMap.put(clientConfigResponse.getId(),clientConfigResponse);
            }
        }
    }

    private void initSubmissionFluxes() {
        for (String clientId : clientConfigResponseMap.keySet()) {
            Flux<Long> submissionsInboxItemCount = submissionChangesPublisherContainer.getFlux()
                    .filter(this::submissionStateChanged)
                    .map(submissionChangesEvent -> getTotalSubmissionCountByState(clientId, SubmissionState.INBOX, Boolean.FALSE))
                    .publish()
                    .autoConnect();
            clientSubmissionsInboxItemCount.put(clientId, submissionsInboxItemCount);

            Flux<Long> submissionsInReviewItemCount = submissionChangesPublisherContainer.getFlux()
                    .filter(this::submissionStateChanged)
                    .map(submissionChangesEvent -> getTotalSubmissionCountByState(clientId, SubmissionState.IN_REVIEW, Boolean.FALSE))
                    .publish()
                    .autoConnect();
            clientSubmissionsInReviewItemCount.put(clientId, submissionsInReviewItemCount);

            Flux<Long> submissionsQuotedItemCount = submissionChangesPublisherContainer.getFlux()
                    .filter(this::submissionStateChanged)
                    .map(submissionChangesEvent -> getTotalSubmissionCountByState(clientId, SubmissionState.QUOTED, Boolean.FALSE))
                    .publish()
                    .autoConnect();
            clientSubmissionsQuotedItemCount.put(clientId, submissionsQuotedItemCount);


            Flux<Long> submissionsDoneItemCount = submissionChangesPublisherContainer.getFlux()
                    .filter(this::submissionStateChanged)
                    .map(submissionChangesEvent -> getTotalSubmissionCountByState(clientId, SubmissionState.DONE, Boolean.FALSE))
                    .publish()
                    .autoConnect();
            clientSubmissionsDoneItemCount.put(clientId, submissionsDoneItemCount);
        }
    }

    private void initSubmissionChanges() {
        for(String clientId : clientConfigResponseMap.keySet())
        {
            Flux<SubmissionsChange> submissionsBoardChangeFlux = submissionChangesPublisherContainer.getFlux()
                    .filter(event -> event.getClientId().equals(clientId)) // Filter by clientId
                    .map(this::getSubmissionBoardStateChanges)
                    .publish()
                    .autoConnect();
            clientSubmissionStateChange.put(clientId,submissionsBoardChangeFlux);
        }
    }

    private SubmissionsChange getSubmissionBoardStateChanges(SubmissionChangeEventDto submissionChangesEvent) {
        LOG.info("Get Submission board changes for submission id : {}",submissionChangesEvent.getSubmissionId());
        Submission submission = getSubmission(submissionChangesEvent);
        UserInfo userInfo = null;
        if(submission!=null && !StringUtils.isEmpty(submission.getAssigneeId())) {
            BasicResponse<TokenResponse> tokenResponse= accessManagementConnector.getToken(submission.getClientId());
            if(HttpStatus.OK.value()==tokenResponse.getStatus()&& tokenResponse.getResult()!=null)
            {
                BasicResponse<UserInfo> userInfoBasicResponse=  questInsightsConnector.getUserById(submission.getAssigneeId(),tokenResponse.getResult().getAccessToken(), submission.getClientId());
                userInfo= HttpStatus.OK.value()==userInfoBasicResponse.getStatus()?userInfoBasicResponse.getResult(): null;
            }
            else
            {
                LOG.error("Error while fetching token for clientId {} due to {}",submission.getClientId(),tokenResponse.getError());
            }
        }
        User user=User.getInstance(userInfo);
        SubmissionSummary previousSubmission = SubmissionSummary.from(submissionChangesEvent);
        SubmissionDetail submissionDetail = null;
        if(submission!=null) {
             submissionDetail = SubmissionDetail.getInstance(submission, user, submission.getRiskInsight());
        }
        SubmissionsChange submissionsChange = new SubmissionsChange(submissionChangesEvent.getSubmissionId(),submissionChangesEvent.getUpdateType().toString(),submissionDetail,previousSubmission);
        LOG.info("Send Submission change event for submission id : {}",submissionsChange.getSubmissionId());
        return submissionsChange;
    }

    @Nullable
    private Submission getSubmission(SubmissionChangeEventDto submissionChangesEvent) {
        BasicResponse<Submission> submissionBasicResponse = submissionManagerConnector.getSubmissionById(submissionChangesEvent.getSubmissionId(), submissionChangesEvent.getClientId(),true);
        if(submissionBasicResponse!=null && HttpStatus.OK.value()==submissionBasicResponse.getStatus() && submissionBasicResponse.getResult()!=null)
        {
           return submissionBasicResponse.getResult();
        }
        LOG.info("Submission not found");
        return null;
    }

    private Long getTotalSubmissionCountByState(String clientId, SubmissionState submissionState, Boolean showArchived) {
        if(!clientConfigResponseMap.containsKey(clientId))
        {
            initClientConfigMap();
        }
        BasicResponse<Long> response = submissionManagerConnector.getSubmissionCountByState(clientId, submissionState, showArchived);
        if (HttpStatus.OK.value() == response.getStatus()) {
            return response.getResult();
        }
        return 0L;
    }

    private boolean submissionStateChanged(SubmissionChangeEventDto submissionChangesEvent) {
        Submission submission =  getSubmission(submissionChangesEvent);
        if(!UpdateType.CREATED.equals(submissionChangesEvent.getUpdateType())  && submissionChangesEvent.getPreviousSubmission() !=null && submission!=null)
        {
         return ! submission.getState().equals(submissionChangesEvent.getPreviousSubmission().getState());
        }
        else {
            return true;
        }
    }

    public Publisher<Long> getSubmissionItemCountPublisher(String clientId, String submissionState) {

        if (SubmissionState.INBOX.name().equals(submissionState)) {
            return clientSubmissionsInboxItemCount.get(clientId);
        } else if (SubmissionState.IN_REVIEW.name().equals(submissionState)) {
            return clientSubmissionsInReviewItemCount.get(clientId);
        } else if (SubmissionState.QUOTED.name().equals(submissionState)) {
            return clientSubmissionsQuotedItemCount.get(clientId);
        } else {
            return clientSubmissionsDoneItemCount.get(clientId);
        }
    }

    public void notifySubscribersAboutChanges(@Valid SubmissionChangeEventDto submissionChangesEvent) {
        submissionChangesPublisherContainer.onNext(submissionChangesEvent);
    }

    public Publisher<SubmissionsChange> getSubmissionChanges(String clientId) {
       return clientSubmissionStateChange.get(clientId);
    }


    private static class PublisherContainer<T> {
        private final UnicastProcessor<T> processor = UnicastProcessor.create();
        private final Flux<T> flux = processor.publish().autoConnect();

        public PublisherContainer() {
        }

        public UnicastProcessor<T> getProcessor() {
            return processor;
        }

        public Flux<T> getFlux() {
            return flux;
        }

        void onNext(T t) {
            processor.onNext(t);
        }
    }

}
