package com.concirrus.submissions.integration;

import com.amazonaws.util.CollectionUtils;
import com.concirrus.quest.common.rest.model.BasicResponse;
import com.concirrus.submission.connector.accessmanagement.AccessManagementConnector;
import com.concirrus.submission.connector.accessmanagement.model.ClientConfigResponse;
import com.concirrus.submission.connector.accessmanagement.model.UserInfoResponse;
import com.concirrus.submission.connector.accessmanagement.model.UserResponseDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class AccessManagementSal {

    AccessManagementConnector accessManagementConnector;
    private static final Logger log = LoggerFactory.getLogger(AccessManagementSal.class);

    public AccessManagementSal(AccessManagementConnector accessManagementConnector) {
        this.accessManagementConnector = accessManagementConnector;
    }

    public List<String> getAllClientIds() {
        BasicResponse<List<ClientConfigResponse>> clientsConfig = accessManagementConnector.getClientsConfig();
        if (HttpStatus.OK.value() == clientsConfig.getStatus() && !CollectionUtils.isNullOrEmpty(clientsConfig.getResult())) {
            return clientsConfig.getResult().stream().map(ClientConfigResponse::getClientId).collect(Collectors.toList());
        } else {
            return null;
        }
    }

    public UserInfoResponse getUserInfo(String clientId,String token) {
        BasicResponse<UserInfoResponse> userInfoResponse = accessManagementConnector.getUserInfo(clientId,token);
        if (HttpStatus.OK.value() == userInfoResponse.getStatus() && !Objects.isNull(userInfoResponse.getResult())) {
           return userInfoResponse.getResult();
        } else {
            return null;
        }
    }

    public BasicResponse<UserInfoResponse> getUserById(String userId,String clientId,String token) {
        return accessManagementConnector.getUserById(userId,clientId,token);
    }

    public List<UserResponseDto> getAllUsers(String clientId, String token) {
        BasicResponse<List<UserResponseDto>> usersResponse = accessManagementConnector.getAllUsers(clientId,token);
        if (HttpStatus.OK.value() == usersResponse.getStatus() && !Objects.isNull(usersResponse.getResult())) {
            return usersResponse.getResult();
        } else {
            log.error("Error while fetching all users for client id : {}",clientId);
            return null;
        }
    }
}
