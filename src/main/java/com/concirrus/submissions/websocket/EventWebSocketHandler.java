package com.concirrus.submissions.websocket;

import com.concirrus.submissions.common.CommonConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Configuration
public class EventWebSocket<PERSON>andler extends TextWebSocketHandler {
    private final static int MAX_SESSIONS = 50; // Max limit of sessions
    private final static ConcurrentHashMap<String, List<WebSocketSession>> clientSessions = new ConcurrentHashMap<>();
    private final ScheduledExecutorService sessionCleanupExecutor = Executors.newSingleThreadScheduledExecutor();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        String clientId = (String) session.getAttributes().get(CommonConstants.CLIENT_ID_HEADER);
        if (clientId != null) {
            // Check if we have exceeded the max session limit
            List<WebSocketSession>existingSessions = clientSessions.get(clientId)!=null?clientSessions.get(clientId):new ArrayList<>();
            if (existingSessions.size() >= MAX_SESSIONS) {
                evictOldestSessionByClientId(clientId);  // Evict oldest session if we hit the max limit
            }
            existingSessions.add(session);
            clientSessions.put(clientId, existingSessions);
            System.out.println("Connection established for clientId: " + clientId +"sessionId: "+session.getId());
        }
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        String clientId = (String) session.getAttributes().get(CommonConstants.CLIENT_ID_HEADER);
        if (clientId != null) {
            List<WebSocketSession>sessionList = clientSessions.get(clientId);
            if (sessionList!=null&&!sessionList.isEmpty()){
                List<WebSocketSession>newSessionLists = sessionList.stream().filter(s-> !s.getId().equals(session.getId())).collect(Collectors.toList());
                clientSessions.put(clientId,newSessionLists);
                System.out.println("Connection" +session.getId()+ "closed for clientId: " + clientId);
                System.out.println("Connection " +session.getId()+ " closed for clientId: " + clientId +
                        ", Status: " + status.getCode() +
                        ", Reason: " + status.getReason());
            }
        }
    }

    public void sendMessageToClient(String clientId, String message) {
        List<WebSocketSession> openSessions = clientSessions.get(clientId)!=null?clientSessions.get(clientId):new ArrayList<>();
        System.out.println("Current client sessions: " + openSessions.size());
        for (WebSocketSession session : openSessions) {
            if (session != null && session.isOpen()) {
                try {
                    session.sendMessage(new TextMessage(message));
                    System.out.println("Message sent to clientId: " + clientId);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                System.out.println("No active session for clientId: " + clientId);
            }
        }
    }

    // Cleanup dead sessions periodically
    private void cleanupSessions() {
        Iterator<Map.Entry<String, List<WebSocketSession>>> iterator = clientSessions.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, List<WebSocketSession>> entry = iterator.next();
            List<WebSocketSession> sessions = entry.getValue();
            for (WebSocketSession session : sessions) {
                if (session == null || !session.isOpen()) {
                    iterator.remove(); // Remove the session if it's closed or not valid
                    System.out.println("Removed dead session for clientId: " + entry.getKey());
                }
            }
        }
    }

    // Method to evict the oldest session when the session limit is exceeded
//    private void evictOldestSession() {
//        Iterator<Map.Entry<String, List<WebSocketSession>>> iterator = clientSessions.entrySet().iterator();
//        if (iterator.hasNext()) {
//            Map.Entry<String, WebSocketSession> entry = iterator.next();
//            WebSocketSession oldestSession = entry.getValue();
//            if (oldestSession != null && oldestSession.isOpen()) {
//                try {
//                    // Optionally, you can close the session if required
//                    oldestSession.close(CloseStatus.GOING_AWAY);
//                    System.out.println("Evicted session for clientId: " + entry.getKey());
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//                iterator.remove(); // Remove the oldest session
//            }
//        }
//    }
    private void evictOldestSessionByClientId(String clientId) {
       List<WebSocketSession>webSocketSessionList = clientSessions.get(clientId);
        if (webSocketSessionList!=null&&!webSocketSessionList.isEmpty()){
            WebSocketSession oldestSession = webSocketSessionList.get(0);
            if (oldestSession != null && oldestSession.isOpen()) {
                try {
                    // Optionally, you can close the session if required
                    oldestSession.close(CloseStatus.GOING_AWAY);
                    System.out.println("Evicted session for clientId: "+ clientId);
                } catch (Exception e) {
                    System.out.println("Error in Evicting  session for clientId: " +clientId);
                    e.printStackTrace();
                }
            }
        }else {
            System.out.println("Cannot Evict oldest session for clientId: "+ clientId + "as there are no active sessions");

        }
    }
    @Scheduled(fixedRate = 10000) // Sends heartbeat every 10 seconds
    public void sendHeartbeats() {
        for (Map.Entry<String, List<WebSocketSession>> entry : clientSessions.entrySet()) {
            List<WebSocketSession> sessionList = entry.getValue();
            for (WebSocketSession session : sessionList ) {
                if (session != null && session.isOpen()) {
                    try {
                        session.sendMessage(new TextMessage("heartbeat"));
                    } catch (IOException e) {
                        System.err.println("Error sending heartbeat to client " + entry.getKey());
                    }
                }
            }
        }
    }

    // Start session cleanup task on initialization
    @PostConstruct
    public void startCleanupTask() {
        sessionCleanupExecutor.scheduleAtFixedRate(this::cleanupSessions, 0, 1, TimeUnit.HOURS); // Cleanup every minute
    }

    // Shutdown the cleanup task when the application is stopped
    @PreDestroy
    public void stopCleanupTask() {
        sessionCleanupExecutor.shutdown();
    }
}


