package com.concirrus.submissions.websocket;

import com.concirrus.submissions.common.CommonConstants;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import java.net.URI;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

public class ClientIdHandshakeInterceptor implements HandshakeInterceptor {

//    @Override
//    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response,
//                                   WebSocketHandler wsHandler, Map<String, Object> attributes) {
//        // Extract `clientId` from query param
//        Map<String, String> queryParams = getQueryParams(request.getURI());
//        String clientId = queryParams.get(CommonConstants.CLIENT_ID_HEADER);
//        if (clientId != null) {
//            attributes.put(CommonConstants.CLIENT_ID_HEADER, clientId);
//            return true;
//        } else {
//            // Reject connection if clientId is missing
//            return false;
//        }
//    }
@Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response,
                                   WebSocketHandler wsHandler, Map<String, Object> attributes){
    try {
        Map<String, String> queryParams = getQueryParams(request.getURI());
        String clientId = queryParams.get(CommonConstants.CLIENT_ID_HEADER);
        if (clientId != null) {
            attributes.put(CommonConstants.CLIENT_ID_HEADER, clientId);
            return true;
        } else {
            return false; // Reject connection if clientId is missing
        }
    } catch (Exception e) {
        System.err.println("Error during handshake: " + e.getMessage());
        return false; // Prevent session from getting stuck
    }
}

    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response,
                               WebSocketHandler wsHandler, Exception exception) {
        // No post-handshake action required
    }

    private Map<String, String> getQueryParams(URI uri) {
        Map<String, String> params = new HashMap<>();
        String query = uri.getQuery();
        if (query != null) {
            for (String param : query.split("&")) {
                String[] parts = param.split("=", 2);
                String key = URLDecoder.decode(parts[0], StandardCharsets.UTF_8);
                String value = parts.length > 1 ? URLDecoder.decode(parts[1], StandardCharsets.UTF_8) : "";
                params.put(key, value);
            }
        }
        return params;
    }
}
