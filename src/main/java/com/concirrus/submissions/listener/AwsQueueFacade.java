package com.concirrus.submissions.listener;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.aws.messaging.listener.SqsMessageDeletionPolicy;
import org.springframework.cloud.aws.messaging.listener.annotation.SqsListener;
import org.springframework.stereotype.Service;

@Service
@ConditionalOnProperty(value = "cloud.provider", havingValue = "aws")
@Slf4j
public class AwsQueueFacade {
    private static final String MESSAGE = "Message";

    private final QueueFacade queueFacade;
    private final ObjectMapper objectMapper;

    public AwsQueueFacade(QueueFacade queueFacade, ObjectMapper objectMapper) {
        this.queueFacade = queueFacade;
        this.objectMapper = objectMapper;
    }

    @SqsListener(value = "${cloud.queue.in.submission-subscription}", deletionPolicy = SqsMessageDeletionPolicy.ON_SUCCESS)
    public void receiveSubmission(String messageFromSns) throws JsonProcessingException {
        JsonNode messageJson = objectMapper.readTree(messageFromSns);
        String message = messageJson.get(MESSAGE).asText();
        queueFacade.receiveSubmission(message);
    }

    @SqsListener(value = "${queue.url.in.submission-handler-subscription}", deletionPolicy = SqsMessageDeletionPolicy.ON_SUCCESS)
    public void receiveAviationSubmission(String message) {
        log.info("AWS Queue Facade : receiveWebsocketEventsSubmission");
        queueFacade.receiveAviationSubmission(message);
    }
}
