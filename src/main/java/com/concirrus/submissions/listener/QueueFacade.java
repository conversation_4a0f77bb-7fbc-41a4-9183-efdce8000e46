package com.concirrus.submissions.listener;

import com.concirrus.submission.connector.submissionmanager.model.SubmissionChangeEventDto;
import com.concirrus.submissions.integration.AviationSubmissionChangesService;
import com.concirrus.submissions.integration.AviationWarSubmissionChangeService;
import com.concirrus.submissions.integration.ConstructionSubmissionChangesService;
import com.concirrus.submissions.integration.SubmissionChangesService;
import com.concirrus.submissionservice.connector.submissionhandler.model.AviationSubmissionChangeEventDto;
import com.concirrus.submissionservice.connector.submissionhandler.model.ConstructionSubmissionChangeEventDto;
import com.concirrus.submissionservice.connector.submissionhandler.model.ProductType;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class QueueFacade {
    private static final Logger log = LoggerFactory.getLogger(QueueFacade.class);

    public static final String MESSAGE = "Message";

    private final ObjectMapper objectMapper;
    private final SubmissionChangesService submissionChangesService;
    private final AviationSubmissionChangesService aviationSubmissionChangesService;
    private final ConstructionSubmissionChangesService constructionSubmissionChangesService;
    private final AviationWarSubmissionChangeService aviationWarSubmissionChangeService;

    public QueueFacade(
            ObjectMapper objectMapper,
            SubmissionChangesService submissionChangesService,
            AviationSubmissionChangesService aviationSubmissionChangesService,
            ConstructionSubmissionChangesService constructionSubmissionChangesService, AviationWarSubmissionChangeService aviationWarSubmissionChangeService
    ) {
        this.objectMapper = objectMapper;
        this.submissionChangesService = submissionChangesService;
        this.aviationSubmissionChangesService = aviationSubmissionChangesService;
        this.constructionSubmissionChangesService = constructionSubmissionChangesService;
        this.aviationWarSubmissionChangeService = aviationWarSubmissionChangeService;
    }

    public void receiveSubmission(String message) {
        try {
            log.info("Received event: {}", message);
            SubmissionChangeEventDto submissionChangeEventDto = objectMapper.readValue(message, SubmissionChangeEventDto.class);
            submissionChangesService.notifySubscribersAboutChanges(submissionChangeEventDto);
            log.info("Exiting Method : {}", "receiveSubmission");
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    public void receiveAviationSubmission(String message) {
        try {
            log.info("Received event: {}", message);
            AviationSubmissionChangeEventDto submissionChangeEventDto = objectMapper.readValue(message, AviationSubmissionChangeEventDto.class);
            if (ProductType.AVIATION.equals(submissionChangeEventDto.getType())) {
                aviationSubmissionChangesService.notifySubscribersAboutChanges(submissionChangeEventDto);
            } else if (ProductType.CONSTRUCTION.equals(submissionChangeEventDto.getType())) {
                log.info("Receive construction");
                ConstructionSubmissionChangeEventDto constructionSubmissionChangeEventDto = objectMapper.readValue(message, ConstructionSubmissionChangeEventDto.class);
                constructionSubmissionChangesService.notifySubscribersAboutChanges(constructionSubmissionChangeEventDto);
            } else if (ProductType.AVIATION_WAR.equals(submissionChangeEventDto.getType())){
                log.info("Received Aviation War change event");
                com.concirrus.submissionservice.connector.submissionhandler.model.SubmissionChangeEventDto changeEventDto = objectMapper.readValue(message, com.concirrus.submissionservice.connector.submissionhandler.model.SubmissionChangeEventDto.class);
                aviationWarSubmissionChangeService.notifySubscribersAboutChanges(changeEventDto);
            }
            else {
                log.info("Product not supported {}", submissionChangeEventDto.getType().name());
            }
            log.info("Exiting Method : {}", "receiveAviationSubmission");
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    public void receiveWebsocketEvents(String message) {
        try {
            log.info("Received Websocket Event: {}", message);
            AviationSubmissionChangeEventDto submissionChangeEventDto = objectMapper.readValue(message, AviationSubmissionChangeEventDto.class);
            if (ProductType.AVIATION_WAR.equals(submissionChangeEventDto.getType())){
                log.info("Received Aviation War change event");
                com.concirrus.submissionservice.connector.submissionhandler.model.SubmissionChangeEventDto changeEventDto = objectMapper.readValue(message, com.concirrus.submissionservice.connector.submissionhandler.model.SubmissionChangeEventDto.class);
                aviationWarSubmissionChangeService.sendWebsocketEvents(changeEventDto);
            }
            log.info("Exiting Method : {}", "receiveWebsocketEvents");
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }
}
