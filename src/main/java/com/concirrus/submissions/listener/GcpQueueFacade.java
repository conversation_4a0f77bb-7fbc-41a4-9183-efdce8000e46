package com.concirrus.submissions.listener;

import com.google.cloud.spring.pubsub.core.PubSubTemplate;
import com.google.cloud.spring.pubsub.integration.AckMode;
import com.google.cloud.spring.pubsub.integration.inbound.PubSubInboundChannelAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Service;

@Service
@ConditionalOnProperty(value = "cloud.provider", havingValue = "gcp")
@Slf4j
public class GcpQueueFacade {
    private final QueueFacade queueFacade;

    public GcpQueueFacade(QueueFacade queueFacade) {
        this.queueFacade = queueFacade;
    }

    @Bean
    public DirectChannel submissionSubscriptionChannel() {
        return new DirectChannel();
    }
    @Bean
    public DirectChannel submissionWebsocketChannel() {
        return new DirectChannel();
    }

    @Bean
    public DirectChannel submissionHandlerSubscriptionChannel() {
        return new DirectChannel();
    }

    @Bean
    public PubSubInboundChannelAdapter submissionSubscriptionChannelAdapter(MessageChannel submissionSubscriptionChannel, PubSubTemplate pubSubTemplate, @Value("${cloud.queue.in.submission-subscription}") String submissionSubscriptionQueue) {
        PubSubInboundChannelAdapter adapter = new PubSubInboundChannelAdapter(pubSubTemplate, submissionSubscriptionQueue + "-sub");
        adapter.setOutputChannel(submissionSubscriptionChannel);
        adapter.setAckMode(AckMode.AUTO);
        return adapter;
    }

    @Bean
    public PubSubInboundChannelAdapter submissionHandlerSubscriptionChannelAdapter(MessageChannel submissionHandlerSubscriptionChannel, PubSubTemplate pubSubTemplate, @Value("${cloud.queue.in.submission-handler-subscription}") String submissionHandlerSubscriptionQueue) {
        PubSubInboundChannelAdapter adapter = new PubSubInboundChannelAdapter(pubSubTemplate, submissionHandlerSubscriptionQueue + "-sub");
        adapter.setOutputChannel(submissionHandlerSubscriptionChannel);
        adapter.setAckMode(AckMode.AUTO);
        return adapter;
    }

    @Bean
    public PubSubInboundChannelAdapter submissionWebsocketChannelAdapter(MessageChannel submissionWebsocketChannel, PubSubTemplate pubSubTemplate, @Value("${cloud.queue.in.submission-websocket-events}") String submissionWebSocketEventsQueue) {
        PubSubInboundChannelAdapter adapter = new PubSubInboundChannelAdapter(pubSubTemplate, submissionWebSocketEventsQueue + "-sub");
        adapter.setOutputChannel(submissionWebsocketChannel);
        adapter.setAckMode(AckMode.AUTO);
        return adapter;
    }

    @ServiceActivator(inputChannel = "submissionSubscriptionChannel")
    public void receiveSubmission(String message) {
        queueFacade.receiveSubmission(message);
    }

    @ServiceActivator(inputChannel = "submissionHandlerSubscriptionChannel")
    public void receiveAviationSubmission(String message) {
        log.info("GCP Queue Facade : receiveAviationSubmission");
        queueFacade.receiveAviationSubmission(message);
    }

    @ServiceActivator(inputChannel = "submissionWebsocketChannel")
    public void receiveWebsocketEventsSubmission(String message) {
        log.info("GCP Queue Facade : receiveWebsocketEventsSubmission");
        queueFacade.receiveWebsocketEvents(message);
    }

}
