package com.concirrus.submissions.controller;

import com.concirrus.quest.common.rest.model.BasicResponse;
import com.concirrus.quest.common.rest.model.Response;
import com.concirrus.submission.connector.submissionmanager.SubmissionManagerConnector;
import com.concirrus.submission.connector.submissionmanager.model.AdditionalInfo;
import com.concirrus.submissions.common.CommonConstants;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("submissions")
public class SubmissionController {
    private final SubmissionManagerConnector submissionManagerConnector;

    public SubmissionController(SubmissionManagerConnector submissionManagerConnector) {
        this.submissionManagerConnector = submissionManagerConnector;
    }

    @GetMapping("/{submissionId}")
    public ResponseEntity<BasicResponse<AdditionalInfo>> getAdditionalInfo(@PathVariable String submissionId, @RequestParam(defaultValue = "false") Boolean showArchived, HttpServletRequest httpServletRequest) {
        String clientId = httpServletRequest.getHeader(CommonConstants.CLIENT_ID_HEADER);
        BasicResponse<AdditionalInfo> response = submissionManagerConnector.getAdditionalInfo(submissionId, clientId, showArchived);
        if (HttpStatus.OK.value()==response.getStatus()) {
            return Response.ok(response.getResult());
        } else {
            return Response.serverError(response.getError());
        }
    }
}
