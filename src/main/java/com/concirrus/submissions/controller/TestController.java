package com.concirrus.submissions.controller;

import com.concirrus.submissions.listener.QueueFacade;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("changes")
public class TestController {
    private static final Logger log = LoggerFactory.getLogger(TestController.class);

    private final QueueFacade queueFacade;

    public TestController(QueueFacade queueFacade) {
        this.queueFacade = queueFacade;
    }

    @PostMapping("/test")
    public void change(@RequestBody String event) {
        queueFacade.receiveSubmission(event);
    }

    @PostMapping("/test2")
    public void change2(@RequestBody String event) {
        queueFacade.receiveAviationSubmission(event);
    }
}
