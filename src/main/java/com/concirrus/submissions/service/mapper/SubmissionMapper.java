package com.concirrus.submissions.service.mapper;

import com.concirrus.submission.connector.submissionmanager.model.SubmissionRequest;
import com.concirrus.submission.connector.submissionmanager.model.VesselInfo;
import com.concirrus.submissions.graphql.model.NodeInput;
import com.concirrus.submissions.graphql.model.SubmissionEditRequest;
import com.concirrus.submissions.model.SubmissionCreationRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class SubmissionMapper {

    SubmissionMapper() {
    }

    public com.concirrus.submission.connector.submissionmanager.model.SubmissionRequest getInstance(SubmissionEditRequest editSubmissionRequest,String clientId) {
        com.concirrus.submission.connector.submissionmanager.model.SubmissionRequest submissionRequest = new SubmissionRequest();
        submissionRequest.setClientId(clientId);
        submissionRequest.setProductName(editSubmissionRequest.getProductName());
        submissionRequest.setProductLine(editSubmissionRequest.getProductLine());
        if(!CollectionUtils.isEmpty(editSubmissionRequest.getVessels())) {
            submissionRequest.setVessels(
                    editSubmissionRequest.getVessels().stream()
                            .map(vesselInfoInput -> {
                                VesselInfo vesselInfo = new VesselInfo();
                                vesselInfo.setVesselId(vesselInfoInput.getVesselId());
                                vesselInfo.setImo(vesselInfoInput.getImo());
                                vesselInfo.setName(vesselInfoInput.getName());
                                vesselInfo.setProductType(vesselInfoInput.getProductType());
                                vesselInfo.setCoverFromDate(vesselInfoInput.getCoverFromDate());
                                vesselInfo.setCoverToDate(vesselInfoInput.getCoverToDate());
                                vesselInfo.setDeadWeightTonnage(vesselInfoInput.getDeadWeightTonnage());
                                vesselInfo.setGrossTonnage(vesselInfoInput.getGrossTonnage());
                                vesselInfo.setYearOfBuild(vesselInfoInput.getYearOfBuild());
                                vesselInfo.setPremium(vesselInfoInput.getPremium());
                                vesselInfo.setDeductible(vesselInfoInput.getDeductible());
                                vesselInfo.setSumInsured(vesselInfoInput.getSumInsured());
                                vesselInfo.setIncreasedValue(vesselInfoInput.getIncreasedValue());
                                vesselInfo.setPremiumCurrencyCode(vesselInfoInput.getPremiumCurrencyCode());
                                vesselInfo.setSumInsuredCurrencyCode(vesselInfoInput.getSumInsuredCurrencyCode());
                                vesselInfo.setDeductibleCurrencyCode(vesselInfoInput.getDeductibleCurrencyCode());
                                vesselInfo.setProbability(1.0);
                                vesselInfo.setNonImo(vesselInfoInput.getNonImo());
                                vesselInfo.setCallSign(vesselInfoInput.getCallSign());
                                vesselInfo.setMmsi(vesselInfoInput.getMmsi());
                                vesselInfo.setFlag(vesselInfoInput.getFlag());
                                vesselInfo.setBreadth(vesselInfoInput.getBreadth());
                                vesselInfo.setWidth(vesselInfoInput.getWidth());
                                vesselInfo.setVesselClass(vesselInfoInput.getVesselClass());
                                vesselInfo.setAdditionalAttributes(vesselInfoInput.getAdditionalAttributes());
                                return vesselInfo;
                            }).collect(Collectors.toList())
            );
        }
        else
        {
            submissionRequest.setVessels(Collections.emptyList());
        }
        submissionRequest.setBrokerName(editSubmissionRequest.getBrokerName());
        submissionRequest.setBrokingHouse(editSubmissionRequest.getBrokingHouse());
        submissionRequest.setAccountName(editSubmissionRequest.getAccountName());
        submissionRequest.setAdditionalInfo(getAdditionalInfo(editSubmissionRequest.getPolicyInfo()));
        return submissionRequest;
    }

    public com.concirrus.submission.connector.submissionmanager.model.SubmissionRequest getInstance(String clientId, SubmissionCreationRequest submissionCreationRequest) {
        com.concirrus.submission.connector.submissionmanager.model.SubmissionRequest submissionRequest = new SubmissionRequest();
        submissionRequest.setClientId(clientId);
        submissionRequest.setProductName(submissionCreationRequest.getProductName());
        submissionRequest.setProductLine(submissionCreationRequest.getProductLine());
        if(!CollectionUtils.isEmpty(submissionCreationRequest.getVessels())) {
            submissionRequest.setVessels(
                    submissionCreationRequest.getVessels().stream()
                            .map(vesselInfoInput -> {
                                VesselInfo vesselInfo = new VesselInfo();
                                vesselInfo.setVesselId(vesselInfoInput.getVesselId());
                                vesselInfo.setImo(vesselInfoInput.getImo());
                                vesselInfo.setProductType(vesselInfoInput.getProductType());
                                vesselInfo.setCoverFromDate(vesselInfoInput.getCoverFromDate());
                                vesselInfo.setCoverToDate(vesselInfoInput.getCoverToDate());
                                vesselInfo.setDeadWeightTonnage(vesselInfoInput.getDeadWeightTonnage());
                                vesselInfo.setGrossTonnage(vesselInfoInput.getGrossTonnage());
                                vesselInfo.setYearOfBuild(vesselInfoInput.getYearOfBuild());
                                vesselInfo.setPremium(vesselInfoInput.getPremium());
                                vesselInfo.setDeductible(vesselInfoInput.getDeductible());
                                vesselInfo.setSumInsured(vesselInfoInput.getSumInsured());
                                vesselInfo.setIncreasedValue(vesselInfoInput.getIncreasedValue());
                                vesselInfo.setPremiumCurrencyCode(vesselInfoInput.getPremiumCurrencyCode());
                                vesselInfo.setSumInsuredCurrencyCode(vesselInfoInput.getSumInsuredCurrencyCode());
                                vesselInfo.setProbability(1.0);
                                vesselInfo.setDeductibleCurrencyCode(vesselInfoInput.getDeductibleCurrencyCode());
                                vesselInfo.setNonImo(vesselInfoInput.getNonImo());
                                vesselInfo.setCallSign(vesselInfoInput.getCallSign());
                                vesselInfo.setMmsi(vesselInfoInput.getMmsi());
                                vesselInfo.setFlag(vesselInfoInput.getFlag());
                                vesselInfo.setBreadth(vesselInfoInput.getBreadth());
                                vesselInfo.setWidth(vesselInfoInput.getWidth());
                                vesselInfo.setVesselClass(vesselInfoInput.getVesselClass());
                                vesselInfo.setAdditionalAttributes(vesselInfoInput.getAdditionalAttributes());
                                return vesselInfo;
                            }).collect(Collectors.toList())
            );
        }
        submissionRequest.setBrokerName(submissionCreationRequest.getBrokerName());
        submissionRequest.setBrokingHouse(submissionCreationRequest.getBrokingHouse());
        submissionRequest.setAccountName(submissionCreationRequest.getAccountName());
        submissionRequest.setAdditionalInfo(getAdditionalInfo(submissionCreationRequest.getPolicyInfo()));
        return submissionRequest;
    }

    public Map<String, Object> getAdditionalInfo(List<NodeInput> nodeInputList) {
        if (CollectionUtils.isEmpty(nodeInputList)) {
            return null;
        }

        Map<String, Object> additionalInfo = new HashMap<>();

        Map<String, String> policyInfo = new HashMap<>();

        for (NodeInput nodeInput : nodeInputList) {
            policyInfo.put(nodeInput.getKey(), nodeInput.getValue());
        }

        additionalInfo.put("policyInfo", policyInfo);

        return additionalInfo;
    }
}
