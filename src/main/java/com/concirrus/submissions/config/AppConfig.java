package com.concirrus.submissions.config;

import com.concirrus.submission.connector.accessmanagement.AccessManagementConnector;
import com.concirrus.submission.connector.policyintegration.PolicyIntegrationConnector;
import com.concirrus.submission.connector.submissionmanager.SubmissionManagementConnector;
import com.concirrus.submission.connector.submissionmanager.SubmissionManagerConnector;
import com.concirrus.submission.connector.workflowmanager.WorkflowManagerConnector;
import com.concirrus.submission.service.notes.connector.notes.NotesServiceConnector;
import com.concirrus.submissions.connector.questinsights.QuestInsightsConnector;
import com.concirrus.submissionservice.connector.submissionhandler.SubmissionHandlerConnector;
import graphql.schema.visibility.GraphqlFieldVisibility;
import graphql.schema.visibility.NoIntrospectionGraphqlFieldVisibility;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AppConfig {
    @Bean
    public SubmissionManagerConnector submissionManagerConnector() {
        return new SubmissionManagerConnector(SubmissionManagerConnector.URL);
    }

    @Bean
    public WorkflowManagerConnector workflowManagerConnector() {
        return new WorkflowManagerConnector(WorkflowManagerConnector.URL);
    }

    @Bean
    public QuestInsightsConnector questInsightsConnector() {
        return new QuestInsightsConnector(QuestInsightsConnector.URL);
    }

    @Bean
    public NotesServiceConnector notesServiceConnector(){
        return new NotesServiceConnector(NotesServiceConnector.URL);
    }

    @Bean
    public AccessManagementConnector accessManagementConnector() {
        return new AccessManagementConnector(AccessManagementConnector.URL);
    }

    @Bean
    public PolicyIntegrationConnector policyIntegrationConnector() {
        return new PolicyIntegrationConnector(PolicyIntegrationConnector.URL);
    }

    @Bean
    public SubmissionManagementConnector submissionManagementConnector() {
        return new SubmissionManagementConnector(SubmissionManagementConnector.URL);
    }

    @Bean
    public SubmissionHandlerConnector submissionHandlerConnector() {
        return new SubmissionHandlerConnector(SubmissionHandlerConnector.URL);
    }

    @Bean
    @ConditionalOnProperty(value = "graphql.tools.schema-parser-options.introspection-enabled", havingValue = "false")
    GraphqlFieldVisibility disableIntrospection() {
        return new NoIntrospectionGraphqlFieldVisibility();
    }

}