package com.concirrus.submissions.config;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.sqs.AmazonSQSAsync;
import com.amazonaws.services.sqs.AmazonSQSAsyncClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class CloudConfig {

    @Bean
    @Primary
    @ConditionalOnProperty(value = "cloud.provider", havingValue = "aws")
    public AmazonSQSAsync amazonSQSAsync(
            @Value("${cloud.aws.accessKeyId}") String awsAccessKey,
            @Value("${cloud.aws.secretKey}") String awsSecretKey,
            @Value("${cloud.aws.region}") String awsRegion
    ) {
        return AmazonSQSAsyncClientBuilder.standard()
                .withCredentials(new AWSStaticCredentialsProvider(new BasicAWSCredentials(awsAccessKey, awsSecretKey)))
                .withRegion(awsRegion)
                .build();
    }
}
