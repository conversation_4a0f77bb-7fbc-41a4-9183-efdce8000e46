package com.concirrus.submissions.util;

import org.apache.commons.lang3.StringUtils;
import org.owasp.encoder.Encode;
import java.util.regex.Pattern;

public class SanitizeHTML {

    // Regex to detect HTML tags
    private static final Pattern HTML_TAG_PATTERN = Pattern.compile("<[^>]+>");

    private SanitizeHTML() {
    }

    /**
     * Sanitizes a string value to prevent Cross-Site Scripting (XSS) attacks by encoding it for HTML.
     * If the input value is null or empty, returns null.
     *
     * @param value the string value to sanitize
     * @return the sanitized string value, or null if the input value is null or empty
     */
    public static String sanitizeValue(String value) {
        if (StringUtils.isEmpty(value)) {
            return null;
        }

        return containsHtml(value) ? Encode.forHtml(value) : value;
    }

    public static boolean containsHtml(String value) {
        return HTML_TAG_PATTERN.matcher(value).find();
    }
}