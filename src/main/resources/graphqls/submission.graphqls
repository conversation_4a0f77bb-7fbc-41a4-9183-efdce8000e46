extend type Query {
    submissions(showArchived: Boolean = false, startDate: String!, endDate: String!, state: String, productLine : String!, personaId:String!, page: Page, sort: Sort): [SubmissionDetail]
    submissionsByIds(ids: [String]!,productLine : String!, showArchived: Boolean = false, personaId:String!): [Submission]
    submissionsSearch(searchrequest : SubmissionSearchRequest,productLine : String!, personaId:String!,page: Page, sort: Sort) : [SubmissionDetail]
    users(ids : [String]) : [User]
    countSubmissionsByState(state: String!, showArchived: Boolean = false) : Int!
    countSearchedSubmissions(searchRequest : SubmissionSearchRequest!) : Int!
    policiesForAccountBySubmissionId(submissionId: String!) : [PolicySummary]
}

input Page {
    pageIndex: Int = 0
    pageSize: Int = 100
}

input Sort {
    sortBy: String!
    sortOrder: SortOrder!
}

enum SortOrder {
    ASC
    DESC
}

type User {
    userId : String!,
    firstName : String!,
    lastName : String,
    userSites : [UserSite!]
}

type UserSite {
    id: Int,
    site: Site,
    isPrimary: Boolean
}

type Site {
    code: String,
    name: String,
    primaryContactName: String,
    primaryContactEmail: String,
    primaryContactTitle: String,
    displayOrder: Int
}

type RiskInsight {
    score : Int,
    expectedLoss : Float,
    severity: Int,
    frequency : Int
}

type PolicySummary {
    id: String!,
    policyNumber : String
    accountId: String,
    policyType : String!,
    policyStatus : String!
}

type Account {
    name: String!,
    retailSite : String!
}

input SubmissionSearchRequest {
    riskScores : [Int],
    fleetSizeRange : [FleetSizeRangeInput],
    productNames : [String],
    accountNames :  [String],
    statusList : [String],
    stateList :[String],
    #Format 'YYYY-MM-DD'
    startDate: String,
    #Format 'YYYY-MM-DD'
    endDate: String,
    searchText : String
    showArchived: Boolean = false
    #Format 'YYYY-MM-DD'
    inceptionFromDate: String,
    #Format 'YYYY-MM-DD'
    inceptionToDate : String
}

input FleetSizeRangeInput
{
    fromFleetSize : Int!,
    toFleetSize: Int
}


type SubmissionDetail {
    submissionId : String!,
    submissionState : String!,
    status : String!,
    assignedUser : User,
    imos: [String], # to be removed, added for backward compatability
    vessels: [VesselInfo],
    brokerName : String,
    brokerNameProbability: Float!,
    productName : String,
    accountName : String,
    accountNameProbability: Float!,
    receivedDate : String!
    timeUpdated : String
    productLine : String,
    brokingHouse : String,
    brokingHouseProbability: Float!,
    comment : String,
    referenceId : String,
    riskInsight : RiskInsight,
    vesselScheduleStatus: VesselScheduleStatus,
    unavailableRiskScoreVesselCount : Int,
    policyInfo : PolicyInfo,
    fleetSize : Int,
    unrecognizedVesselCount : Int,
    nonImoCount : Int,
}

type PolicyInfo{
    brokeragePercentage : Float,
    totalChargedPremium: Float,
    policyStartDate: String,
    policyEndDate : String
}

type Submission {
    submissionId : String!,
    submissionState : String!,
    status : String!,
    assignedUser : User,
    # to be removed, added for backward compatability
    imos: [String],
    vessels: [VesselInfo],
    productName : String,
    receivedDate : String,
    timeUpdated : String
    productLine : String,
    comment : String,
    referenceId : String,
    riskInsight : RiskInsight,
    brokerName : String,
    brokerNameProbability: Float!,
    accountName : String,
    accountNameProbability: Float!,
    brokingHouse : String,
    brokingHouseProbability: Float!,
    policyInfo: [Node!],
    vesselScheduleStatus: VesselScheduleStatus,
    unavailableRiskScoreVesselCount : Int,
    fleetSize : Int,
    unrecognizedVesselCount : Int,
    nonImoCount : Int,
}

type Node {
    key: String!
    value: String
}

type ValueWithProbability {
    value: String,
    probability: Float!
}

type VesselInfo {
    vesselId: String,
    imo: String,
    name: String,
    productType: String,
    coverFromDate: String,
    coverToDate: String,
    deadWeightTonnage: Float,
    grossTonnage: Float,
    yearOfBuild: Int,
    premium: Float,
    deductible: Float,
    sumInsured: Float,
    increasedValue: Float,
    premiumCurrencyCode: String,
    sumInsuredCurrencyCode: String,
    deductibleCurrencyCode: String,
    probability: Float,
    mmsi: String,
    flag: String,
    vesselClass: String,
    callSign: String,
    breadth: String,
    width: String,
    length: String,
    nonImo: Boolean,
    additionalAttributes: String
}

enum VesselScheduleStatus {
    COMPLETE,INCOMPLETE
}