extend type Query {
    notes(entityId: String!, entityType: EntityType!, noteTypes : [String], subEntityTypes: [String], subEntityIds: [String]): [Note]
}


type Note {
    # Note id
    id: String!
    # Author of the note
    author: User
    # Time of note creation (or last update). Format: ISO8601, e.g. 2005-01-02T03:01:45+01:00
    time: String!
    # Text of note
    text: String!
    # Type of note
    noteType: String!
    # type of subEntity under main entity
    subEntityType: String
    # id of subEntity under main entity
    subEntityId: String
    updatedAt : String
    updatedBy : String

    # note updated by user
    updatedByAuthor: User
}

extend type Mutation {
    notes: NotesMutations!
}

enum EntityType {
    SUBMISSION,
    UNBLOCK_REQUEST,
    SUBMISSION_RATER
}

enum NoteType {
    SUBMISSION,
    REQUEST_NOTE,
    RESPONSE_NOTE

}

enum Type {
    HTML
    TEXT
}

type NotesMutations {
    createNote(entityId: String!, entityType: EntityType!, noteText: String!, type: Type!, subEntityType: String, subEntityId: String, noteType: String): Note
    deleteNote(noteId: String!): Boolean
    updateNote(noteId: String!, noteText: String!): Note
}
