type Subscription {
    # total count of INBOX submissions with no filters applied
    submissionsItemCount(clientId: String!,submissionState:String!, type: String) : Int!,
    # Change on the submissions board
    submissionChanges(
        clientId: String!
    ): SubmissionsChange

    aviationSubmissionChanges(
        clientId: String!
    ): AviationSubmissionsChange

    constructionSubmissionChanges(
        clientId: String!
    ): ConstructionSubmissionsChange
    aviationWarSubmissionChanges(
        clientId: String!
    ):AviationWarSubmissionChange
}

scalar JSON

type SubmissionsChange {
    submissionId: String,
    updateType: String,
    # Submission state on board before change
    updatedSubmission : SubmissionDetail
    previousSubmission : SubmissionSummary
}

type AviationSubmissionsChange {
    submissionId: String,
    updateType: String,
    # Submission state on board before change
    updatedSubmission : AviationSubmissionSummary
    previousSubmission : AviationSubmissionSummary
}

type ConstructionSubmissionsChange {
    submissionId: String,
    updateType: String,
    # Submission state on board before change
    updatedSubmission : ConstructionSubmissionSummary
    previousSubmission : ConstructionSubmissionSummary
}

type AviationWarSubmissionChange {
    submissionId: String,
    updateType: String,
    # Submission state on board before change
    updatedSubmission : AviationWarSubmissionSummary
    previousSubmission : AviationWarSubmissionSummary
}
type AviationWarJobUpdate {
    submissionId: String,
    jobId: String,
    jobStatus: String,
    calculatedTiv: Float,
    numberOfLocations: Int,
    jobResult: AviationWarJobResult
}

type InsuredInfo {
    name: String,
}

type Producer {
    agencyName: String,
    name: String,
    phone: String,
    email: String,
    address: String,
    city: String,
    state: String,
    zip: String
}

type AviationSubmissionSummary{
    submissionId : String,
    state : String,
    status : String,
    assigneeId : String,
    productName : String,
    assignedUser : User,
    businessHeading: String,
    proposedEffectiveDate: String,
    riskScore: Int,
    numberOfInsuredAssets: Int,
    businessLine: String,
    product: String,
    insuredInfo: [InsuredInfo],
    receivedDate: String,
    producer: Producer
    blockedBySubmissionId: String
    isBlocked: Boolean
    blockedSubmissions: [String]
    submissionNumber: String
    fleetSize: Int
    policyInfo: PolicyInformation
}

type ConstructionSubmissionSummary{
    submissionId : String,
    state : String,
    status : String,
    assigneeId : String,
    assignedUser : User,
    insuredName: String,
    policyType: String,
    priorPolicyNumber: String,
    policyTerm: String,
    brokerage: String,
    broker: String,
    productType: String,
    transactionType: String,
    policyExpirationDate: String,
    policyInceptionDate: String,
    policyNumber: String,
    tCreated: String,
    brokerInfo: BrokerInfo,
    constructionInsuredInfo: ConstructionInsuredInfo,
    blockedBySubmissionId: String
    isBlocked: Boolean
    blockedSubmissions: [String]
    submissionNumber: String
}
type AviationWarSubmissionSummary{
    submissionId: String,
    state: String,
    status: String,
    coverageType: String,
    lineOfBusiness: String,
    sourceId: String,
    sourceName: String,
    assignedId : String,
    aviationWarInsuredInfo : AviationWarInsuredInfo,
    brokerInfo : AviationWarBrokerInfo,
    riskDetails : AviationWarRiskDetails,
    assignedUser : User
    tCreated : String,
    submissionNumber : String,
    productType : String
    isBlocked : Boolean
}
type ConstructionInsuredInfo {
    otherNamedInsureds: [String]
    owner: String
    firstName: String
    mailingAddress: JSON
}
type BrokerInfo {
    senderName: String,
    name: String,
    mailingAddress : JSON
}

#Adding new object to give previous submission state for subscription.
type SubmissionSummary{
    submissionId : String!,
    submissionState : String!,
    status : String!,
    brokerName : String,
    productName : String,
    accountName : String,
    brokingHouse : String
}

type PolicyInformation {
    requestedEffectiveDate: String
}
type AviationWarInsuredInfo {
    insuredName:String,
    additionalInsureds:[JSON],
    city:String,
    coverType:String,
    domicile:String,
    state:String,
    street:String,
    website:String,
    zip:String
}
type AviationWarBrokerInfo {
    broker:String,
    brokingHouse:String,
    emailAddress:String,
    phoneNumber:String
}
type AviationWarRiskDetails {
    peril: [String]
    expiryDate: String
    inceptionDate: String
    occupancy: String
    hullLimit: Float
    policyCurrency: String
    premiumCurrency: String
    quotedWarPremium: Float
    boundWarPremium: Float
    tlo: Float
    hijacking: Float
    technicalRecords: Float
    maxHullLimit: Float
    aggregateLimit: Float
    quotedRate: Float
    boundRate: Float
    excess: Float
    hubAirport: String
    totalFleetValue: Float
    averageFleetValue: Float
    averageAircraftValue: Float
}
type AviationWarJobResult {
    calculatedTiv: Float,
    geocodingAccuracy: JSON
}
