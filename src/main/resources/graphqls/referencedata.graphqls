extend type Query {
    # Few list of values used in the API model
    referenceData: ReferenceData!
}

type ReferenceData {
    # List of submission States
    submissionStates: [SubmissionStateResponse]
    # List of submissionStatus
    submissionStatus: [SubmissionStatusResponse]
    # List of status comments
    statusComments(status : String!): [String]
    #list of products for a product Line
    products(productLineId : String): [Product]
    #List of RiskScores
    riskScores : [Int!],
   #List of fleet Sizes in range for filter
    fleetSizes : [FleetSizeRange!]
    #List of submission deletion reasons
    submissionDeletionReasons : [String!]
}

type FleetSizeRange
{
    fromFleetSize : Int!,
    toFleetSize: Int
}

type SubmissionStateResponse {
    state : String,
    value : String
}

type Product
{
    id : String,
    name: String,
    displayName : String
}

type SubmissionStatusResponse {
status : String,
value : String
}