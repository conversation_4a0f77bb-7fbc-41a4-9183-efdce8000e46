extend type Mutation {
    submission: SubmissionMutation!
}

type SubmissionMutation {
    create(submission: SubmissionCreationRequest!): String!
    editSubmission(submissionId: String!, editSubmissionRequest: SubmissionEditRequest!): Boolean
    assignSubmission(submissionId: String!, userId: String): Boolean
    editSubmissionStateStatus(submissionsStateAndStatusChangeRequest: SubmissionStateStatusChangeRequest!, submissionId: String!): Boolean
    deleteSubmission(deleteSubmissionRequest: DeleteSubmissionRequest!) : Boolean
}

input DeleteSubmissionRequest {
    submissionId : String!,
    deleteAssociatedAccount : Boolean!,
    deletionReason : String!
}

input SubmissionStateStatusChangeRequest {
    submissionState: String! ,
    comment : String,
    submissionStatus : String!
}

#Removed imos list as it is part of vessels now.
input SubmissionCreationRequest {
    vessels: [VesselInfoInput!]!
    brokerName : String,
    productName : String,
    accountName : String,
    productLine : String,
    brokingHouse : String,
    policyInfo: [NodeInput!]
}

input SubmissionEditRequest {
    vessels: [VesselInfoInput!]!, # Will be not nullable once imos is removed.
    brokerName : String,
    productName : String,
    accountName : String,
    productLine : String,
    brokingHouse : String,
    policyInfo: [NodeInput!]
}

input VesselInfoInput {
    vesselId: String!,
    imo: String,
    name: String,
    productType: String,
    coverFromDate: String,
    coverToDate: String,
    deadWeightTonnage: Float,
    grossTonnage: Float,
    yearOfBuild: Int,
    premium: Float,
    deductible: Float,
    sumInsured: Float,
    increasedValue: Float,
    premiumCurrencyCode: String,
    sumInsuredCurrencyCode: String,
    deductibleCurrencyCode: String,
    mmsi: String,
    flag: String,
    vesselClass: String,
    callSign: String,
    breadth: String,
    width: String,
    length: String,
    nonImo: Boolean,
    additionalAttributes: String
}

input NodeInput {
    key: String!
    value: String
}