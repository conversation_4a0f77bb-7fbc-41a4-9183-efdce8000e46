app:
  fixed-executor-thread-count: 5
  time-zone: Europe/London
  default-application-group: 82c594e6-d5bd-4e09-bef6-778246279e08

server:
  port: 9005
  servlet:
    context-path: /submission-gateway-service
    session:
      timeout: 180m

management:
  endpoints:
    web:
      base-path: /manage
      exposure:
        include: health, info, prometheus, restart
  endpoint:
    info:
      enabled: true
    health:
      enabled: true
    restart:
      enabled: true

spring:
  application:
    name: submission-gateway-service
  redis:
    url: redis://${REDIS_PASSWORD}@${REDIS_HOST:localhost}:${REDIS_PORT:6379}

service:
  auth:
    url: ${AUTH_URL:https://integration-auth.questmarine.app/auth/realms/marine/protocol/openid-connect/token}

cloud:
  provider: aws
  queue:
    in:
      submission-websocket-events: ${SUBMISSION_WEBSOCKET_EVENTS:submission-websocket-events-dev}
      submission-subscription: ${SUBSCRIPTION_QUEUE:submission-subscription-events}
      submission-handler-subscription: submission-handler-subscription-events-dev
  gcp:
    project-id: ${CLOUD_GCP_PROJECT_ID}
  aws:
    accessKeyId: ${AWS_ACCESS_KEY}
    secretKey: ${AWS_SECRET}
    region: us-east-2

graphql:
  tools:
    schema-parser-options:
      introspection-enabled: ${INTROSPECTION_ENABLED:true}

resilience4j:
  ratelimiter:
    instances:
      notes_limiter:
        limitForPeriod: 10
        limitRefreshPeriod: 1s
        timeoutDuration: 0
