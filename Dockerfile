FROM openjdk:11 as base

WORKDIR /root/project/

# Init Gradle Wrapper
COPY gradle gradle
COPY gradlew ./
RUN ./gradlew --version

# Download dependencies
COPY build.gradle settings.gradle ./
RUN ./gradlew downloadDependencies --no-daemon --build-cache

FROM base as builder
ARG VERSION
ARG COMMIT_HASH
ARG NEXUS_USER
ARG NEXUS_PASSWORD

WORKDIR /root/project/

# Copy source code and build the application
COPY src src
RUN ./gradlew build -Pversion=$VERSION -Pcommit_hash=$COMMIT_HASH -PnexusPassword=$NEXUS_PASSWORD -PnexusUsername=$NEXUS_USER 

# Stage 3: Create Final Runtime Image
FROM openjdk:11 as server
ARG VERSION
ENV JAVA_OPTS="-Xmx256M"
VOLUME /tmp
COPY --from=builder /root/project/build/libs/submission-gateway-service-$VERSION.jar app.jar
ENTRYPOINT java $JAVA_OPTS -Djava.security.egd=file:/dev/./urandom -jar /app.jar
EXPOSE 9005
